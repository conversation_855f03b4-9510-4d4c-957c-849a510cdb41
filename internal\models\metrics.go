package models

import (
	"time"

	"gorm.io/gorm"
)

// ServerMetric represents the server_metrics table
type ServerMetric struct {
	gorm.Model
	ServerID        uint    `gorm:"not null"`
	Server          Server  `gorm:"foreignKey:ServerID"` // Belongs To Server
	CPUUsage        float64 `gorm:"not null"`
	MemoryUsage     float64 `gorm:"not null"`
	MemoryTotal     uint64  `gorm:"not null"`
	MemoryUsed      uint64  `gorm:"not null"`
	DiskUsage       float64 `gorm:"not null"`
	DiskTotal       uint64  `gorm:"not null"`
	DiskUsed        uint64  `gorm:"not null"`
	NetworkInBytes  uint64  `gorm:"default:0"`
	NetworkOutBytes uint64  `gorm:"default:0"`
	LoadAverage1    float64
	LoadAverage5    float64
	LoadAverage15   float64
	ProcessCount    int
	Timestamp       time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}
