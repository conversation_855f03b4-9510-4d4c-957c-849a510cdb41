package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// Config 应用程序配置结构体
type Config struct {
	System    SystemConfig    `yaml:"system" mapstructure:"system"`
	Database  DatabaseConfig  `yaml:"database" mapstructure:"database"`
	Scheduler SchedulerConfig `yaml:"scheduler" mapstructure:"scheduler"`
	Servers   []ServerConfig  `yaml:"servers" mapstructure:"servers"`
	Peer      PeerConfig      `yaml:"peer" mapstructure:"peer"`
	Test      TestConfig      `yaml:"test" mapstructure:"test"`
	Web       WebConfig       `yaml:"web" mapstructure:"web"`
	API       APIConfig       `yaml:"api" mapstructure:"api"`
	Sync      SyncConfig      `yaml:"sync" mapstructure:"sync"`
	Log       LogConfig       `yaml:"log" mapstructure:"log"`
}

// SystemConfig 系统配置结构体
type SystemConfig struct {
	Name        string   `yaml:"name" mapstructure:"name"`
	Version     string   `yaml:"version" mapstructure:"version"`
	Environment string   `yaml:"environment" mapstructure:"environment"`
	DataDir     string   `yaml:"data_dir" mapstructure:"data_dir"`
	PidFile     string   `yaml:"pid_file" mapstructure:"pid_file"`
	Mode        string   `yaml:"mode" mapstructure:"mode"` // server, client, both
	Description string   `yaml:"description" mapstructure:"description"`
	Tags        []string `yaml:"tags" mapstructure:"tags"`
	Interval    int      `yaml:"interval" mapstructure:"interval"`
}

// DatabaseConfig 数据库配置结构体
type DatabaseConfig struct {
	Path             string        `yaml:"path" mapstructure:"path"`
	MaxOpenConns     int           `yaml:"max_open_conns" mapstructure:"max_open_conns"`
	MaxIdleConns     int           `yaml:"max_idle_conns" mapstructure:"max_idle_conns"`
	ConnMaxLifetime  time.Duration `yaml:"conn_max_lifetime" mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime  time.Duration `yaml:"conn_max_idle_time" mapstructure:"conn_max_idle_time"`
	EnableWAL        bool          `yaml:"enable_wal" mapstructure:"enable_wal"`
	EnableForeignKey bool          `yaml:"enable_foreign_key" mapstructure:"enable_foreign_key"`
	BusyTimeout      time.Duration `yaml:"busy_timeout" mapstructure:"busy_timeout"`
	RetentionDays    int           `yaml:"retention_days" mapstructure:"retention_days"`
}

// SchedulerConfig 调度器配置结构体
type SchedulerConfig struct {
	Enabled       bool          `yaml:"enabled" mapstructure:"enabled"`
	Mode          string        `yaml:"mode" mapstructure:"mode"` // odd, even, both
	Timezone      string        `yaml:"timezone" mapstructure:"timezone"`
	StartHour     int           `yaml:"start_hour" mapstructure:"start_hour"`
	EndHour       int           `yaml:"end_hour" mapstructure:"end_hour"`
	MaxConcurrent int           `yaml:"max_concurrent" mapstructure:"max_concurrent"`
	RetryAttempts int           `yaml:"retry_attempts" mapstructure:"retry_attempts"`
	RetryInterval time.Duration `yaml:"retry_interval" mapstructure:"retry_interval"`
}

// ServerConfig 服务器配置结构体
type ServerConfig struct {
	Name     string   `yaml:"name" mapstructure:"name"`
	IP       string   `yaml:"ip" mapstructure:"ip"`
	Port     int      `yaml:"port" mapstructure:"port"`
	Location string   `yaml:"location" mapstructure:"location"`
	Provider string   `yaml:"provider" mapstructure:"provider"`
	Enabled  bool     `yaml:"enabled" mapstructure:"enabled"`
	Priority int      `yaml:"priority" mapstructure:"priority"`
	Tags     []string `yaml:"tags" mapstructure:"tags"`
}

// PeerConfig 对等体配置结构体
type PeerConfig struct {
	IP       string        `yaml:"ip" mapstructure:"ip"`
	Port     int           `yaml:"port" mapstructure:"port"`
	Username string        `yaml:"username" mapstructure:"username"`
	Password string        `yaml:"password" mapstructure:"password"`
	Timeout  time.Duration `yaml:"timeout" mapstructure:"timeout"`
	Enabled  bool          `yaml:"enabled" mapstructure:"enabled"`
}

// TestConfig 测试配置结构体
type TestConfig struct {
	Duration       time.Duration `yaml:"duration" mapstructure:"duration"`
	Parallel       int           `yaml:"parallel" mapstructure:"parallel"`
	WindowSize     string        `yaml:"window_size" mapstructure:"window_size"`
	BufferLength   string        `yaml:"buffer_length" mapstructure:"buffer_length"`
	Bandwidth      string        `yaml:"bandwidth" mapstructure:"bandwidth"`
	Protocol       string        `yaml:"protocol" mapstructure:"protocol"` // tcp, udp, both
	Reverse        bool          `yaml:"reverse" mapstructure:"reverse"`
	Bidir          bool          `yaml:"bidir" mapstructure:"bidir"`
	Timeout        time.Duration `yaml:"timeout" mapstructure:"timeout"`
	ConnectTimeout time.Duration `yaml:"connect_timeout" mapstructure:"connect_timeout"`
	MaxRetries     int           `yaml:"max_retries" mapstructure:"max_retries"`
	RetryDelay     time.Duration `yaml:"retry_delay" mapstructure:"retry_delay"`
}

// WebConfig Web界面配置结构体
type WebConfig struct {
	Enabled     bool      `yaml:"enabled" mapstructure:"enabled"`
	Port        int       `yaml:"port" mapstructure:"port"`
	Host        string    `yaml:"host" mapstructure:"host"`
	StaticDir   string    `yaml:"static_dir" mapstructure:"static_dir"`
	TemplateDir string    `yaml:"template_dir" mapstructure:"template_dir"`
	TLS         TLSConfig `yaml:"tls" mapstructure:"tls"`
}

// APIConfig API服务配置结构体
type APIConfig struct {
	Enabled   bool          `yaml:"enabled" mapstructure:"enabled"`
	Port      int           `yaml:"port" mapstructure:"port"`
	Host      string        `yaml:"host" mapstructure:"host"`
	Prefix    string        `yaml:"prefix" mapstructure:"prefix"`
	Timeout   time.Duration `yaml:"timeout" mapstructure:"timeout"`
	RateLimit int           `yaml:"rate_limit" mapstructure:"rate_limit"`
	TLS       TLSConfig     `yaml:"tls" mapstructure:"tls"`
	CORS      CORSConfig    `yaml:"cors" mapstructure:"cors"`
}

// TLSConfig TLS配置结构体
type TLSConfig struct {
	Enabled  bool   `yaml:"enabled" mapstructure:"enabled"`
	CertFile string `yaml:"cert_file" mapstructure:"cert_file"`
	KeyFile  string `yaml:"key_file" mapstructure:"key_file"`
}

// CORSConfig CORS配置结构体
type CORSConfig struct {
	Enabled      bool     `yaml:"enabled" mapstructure:"enabled"`
	AllowOrigins []string `yaml:"allow_origins" mapstructure:"allow_origins"`
	AllowMethods []string `yaml:"allow_methods" mapstructure:"allow_methods"`
	AllowHeaders []string `yaml:"allow_headers" mapstructure:"allow_headers"`
}

// SyncConfig 同步配置结构体
type SyncConfig struct {
	Enabled       bool          `yaml:"enabled" mapstructure:"enabled"`
	Interval      time.Duration `yaml:"interval" mapstructure:"interval"`
	BatchSize     int           `yaml:"batch_size" mapstructure:"batch_size"`
	MaxRetries    int           `yaml:"max_retries" mapstructure:"max_retries"`
	RetryInterval time.Duration `yaml:"retry_interval" mapstructure:"retry_interval"`
	Timeout       time.Duration `yaml:"timeout" mapstructure:"timeout"`
}

// LogConfig 日志配置结构体
type LogConfig struct {
	Level      string `yaml:"level" mapstructure:"level"`
	Format     string `yaml:"format" mapstructure:"format"` // json, text
	Output     string `yaml:"output" mapstructure:"output"` // stdout, stderr, file
	File       string `yaml:"file" mapstructure:"file"`
	MaxSize    int    `yaml:"max_size" mapstructure:"max_size"`       // MB
	MaxBackups int    `yaml:"max_backups" mapstructure:"max_backups"` // 保留文件数
	MaxAge     int    `yaml:"max_age" mapstructure:"max_age"`         // 天数
	Compress   bool   `yaml:"compress" mapstructure:"compress"`
}

// Manager 配置管理器
type Manager struct {
	config     *Config
	configFile string
	viper      *viper.Viper
	mu         sync.RWMutex
	callbacks  []func(*Config)
}

// NewManager 创建一个新的配置管理器实例
func NewManager(configFile string) *Manager {
	return &Manager{
		configFile: configFile,
		viper:      viper.New(),
	}
}

// Load 从文件或环境变量加载配置
func (m *Manager) Load() error {
	// 设置配置文件路径
	if m.configFile != "" {
		m.viper.SetConfigFile(m.configFile)
	} else {
		// 默认配置文件搜索路径
		m.viper.SetConfigName("config")
		m.viper.SetConfigType("yaml")
		m.viper.AddConfigPath("./configs")
		m.viper.AddConfigPath(".")
		m.viper.AddConfigPath("/etc/monitor")
	}

	// 设置环境变量前缀，允许通过环境变量覆盖配置
	m.viper.SetEnvPrefix("MONITOR")
	m.viper.AutomaticEnv()
	m.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 设置所有配置项的默认值
	m.setDefaults()

	// 从指定路径读取配置文件
	if err := m.viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 如果配置文件不存在，则使用默认配置并打印警告
			fmt.Printf("Warning: Config file not found, using defaults\n")
		} else {
			return fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// 将解析后的配置映射到Config结构体
	config := &Config{}
	if err := m.viper.Unmarshal(config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	m.config = config
	return nil
}

// setDefaults 设置默认值
func (m *Manager) setDefaults() {
	// System defaults
	m.viper.SetDefault("system.name", "Server Monitor")
	m.viper.SetDefault("system.version", "1.0.0")
	m.viper.SetDefault("system.environment", "production")
	m.viper.SetDefault("system.data_dir", "./data")
	m.viper.SetDefault("system.pid_file", "./data/monitor.pid")
	m.viper.SetDefault("system.mode", "both")

	// Database defaults
	m.viper.SetDefault("database.path", "./data/monitor.db")
	m.viper.SetDefault("database.max_open_conns", 25)
	m.viper.SetDefault("database.max_idle_conns", 5)
	m.viper.SetDefault("database.conn_max_lifetime", "1h")
	m.viper.SetDefault("database.conn_max_idle_time", "10m")
	m.viper.SetDefault("database.enable_wal", true)
	m.viper.SetDefault("database.enable_foreign_key", true)
	m.viper.SetDefault("database.busy_timeout", "30s")

	// Scheduler defaults
	m.viper.SetDefault("scheduler.enabled", true)
	m.viper.SetDefault("scheduler.mode", "odd")
	m.viper.SetDefault("scheduler.timezone", "Asia/Shanghai")
	m.viper.SetDefault("scheduler.start_hour", 0)
	m.viper.SetDefault("scheduler.end_hour", 23)
	m.viper.SetDefault("scheduler.max_concurrent", 4)
	m.viper.SetDefault("scheduler.retry_attempts", 3)
	m.viper.SetDefault("scheduler.retry_interval", "5m")

	// Test defaults
	m.viper.SetDefault("test.duration", "30s")
	m.viper.SetDefault("test.parallel", 4)
	m.viper.SetDefault("test.window_size", "64K")
	m.viper.SetDefault("test.buffer_length", "128K")
	m.viper.SetDefault("test.bandwidth", "0")
	m.viper.SetDefault("test.protocol", "tcp")
	m.viper.SetDefault("test.reverse", false)
	m.viper.SetDefault("test.bidir", false)
	m.viper.SetDefault("test.timeout", "60s")
	m.viper.SetDefault("test.connect_timeout", "10s")
	m.viper.SetDefault("test.max_retries", 3)
	m.viper.SetDefault("test.retry_delay", "5s")

	// Web defaults
	m.viper.SetDefault("web.enabled", true)
	m.viper.SetDefault("web.port", 8080)
	m.viper.SetDefault("web.host", "0.0.0.0")
	m.viper.SetDefault("web.static_dir", "./web/static")
	m.viper.SetDefault("web.template_dir", "./web/templates")
	m.viper.SetDefault("web.tls.enabled", false)

	// API defaults
	m.viper.SetDefault("api.enabled", true)
	m.viper.SetDefault("api.port", 8443)
	m.viper.SetDefault("api.host", "0.0.0.0")
	m.viper.SetDefault("api.prefix", "/api/v1")
	m.viper.SetDefault("api.timeout", "30s")
	m.viper.SetDefault("api.rate_limit", 100)
	m.viper.SetDefault("api.tls.enabled", false)
	m.viper.SetDefault("api.cors.enabled", true)
	m.viper.SetDefault("api.cors.allow_origins", []string{"*"})
	m.viper.SetDefault("api.cors.allow_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	m.viper.SetDefault("api.cors.allow_headers", []string{"*"})

	// Sync defaults
	m.viper.SetDefault("sync.enabled", false)
	m.viper.SetDefault("sync.interval", "5m")
	m.viper.SetDefault("sync.batch_size", 100)
	m.viper.SetDefault("sync.max_retries", 3)
	m.viper.SetDefault("sync.retry_interval", "30s")
	m.viper.SetDefault("sync.timeout", "30s")

	// Log defaults
	m.viper.SetDefault("log.level", "info")
	m.viper.SetDefault("log.format", "text")
	m.viper.SetDefault("log.output", "stdout")
	m.viper.SetDefault("log.max_size", 100)
	m.viper.SetDefault("log.max_backups", 3)
	m.viper.SetDefault("log.max_age", 28)
	m.viper.SetDefault("log.compress", true)

	// Peer defaults
	m.viper.SetDefault("peer.port", 8443)
	m.viper.SetDefault("peer.timeout", "30s")
	m.viper.SetDefault("peer.enabled", false)
}

// Get 获取当前加载的配置
func (m *Manager) Get() *Config {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.config
}

// GetViper 获取Viper实例，用于高级配置操作
func (m *Manager) GetViper() *viper.Viper {
	return m.viper
}

// Reload 重新加载配置，等同于再次调用Load方法
func (m *Manager) Reload() error {
	return m.Load()
}

// Save 将当前配置保存到指定文件
func (m *Manager) Save(filename string) error {
	if filename == "" {
		filename = m.configFile
	}

	// 确保配置文件所在的目录存在，如果不存在则创建
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	return m.viper.WriteConfigAs(filename)
}

// OnConfigChange 注册一个回调函数，在配置发生变更时执行
func (m *Manager) OnConfigChange(callback func(*Config)) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.callbacks = append(m.callbacks, callback)
}

// WatchConfig 启动配置文件的监听，当文件内容变化时自动重新加载
func (m *Manager) WatchConfig() {
	m.viper.WatchConfig()
	m.viper.OnConfigChange(func(e fsnotify.Event) {
		fmt.Printf("Config file changed: %s\n", e.Name)

		// 重新加载配置并应用变更
		if err := m.reloadConfig(); err != nil {
			fmt.Printf("Failed to reload config: %v\n", err)
			return
		}

		// 调用所有已注册的回调函数
		m.mu.RLock()
		config := m.config
		callbacks := make([]func(*Config), len(m.callbacks))
		copy(callbacks, m.callbacks)
		m.mu.RUnlock()

		for _, callback := range callbacks {
			callback(config)
		}
	})
}

// reloadConfig 重新加载配置的内部方法，不触发外部回调
func (m *Manager) reloadConfig() error {
	// 将更新后的配置解析到Config结构体
	config := &Config{}
	if err := m.viper.Unmarshal(config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证加载的配置是否有效
	if err := config.Validate(); err != nil {
		return fmt.Errorf("config validation failed: %w", err)
	}

	m.mu.Lock()
	m.config = config
	m.mu.Unlock()

	return nil
}
