package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"server-monitor/internal/monitor"
)

// Hub WebSocket连接管理中心
type Hub struct {
	// 注册的客户端连接
	clients map[*Client]bool

	// 广播消息通道
	broadcast chan []byte

	// 注册客户端通道
	register chan *Client

	// 注销客户端通道
	unregister chan *Client

	// 系统监控器
	systemMonitor *monitor.SystemMonitor

	// 互斥锁
	mu sync.RWMutex

	// 运行状态
	running bool
}

// NewHub 创建新的WebSocket Hub
func NewHub(sysMon *monitor.SystemMonitor) *Hub {
	return &Hub{
		clients:       make(map[*Client]bool),
		broadcast:     make(chan []byte, 256),
		register:      make(chan *Client),
		unregister:    make(chan *Client),
		systemMonitor: sysMon,
		running:       false,
	}
}

// Run 启动Hub
func (h *Hub) Run() {
	h.mu.Lock()
	if h.running {
		h.mu.Unlock()
		return
	}
	h.running = true
	h.mu.Unlock()

	log.Println("WebSocket Hub 启动")

	// 启动定时推送系统信息
	go h.startSystemInfoBroadcast()

	for {
		select {
		case client := <-h.register:
			h.mu.Lock()
			h.clients[client] = true
			h.mu.Unlock()
			log.Printf("客户端连接: %s", client.conn.RemoteAddr())

			// 发送欢迎消息
			welcome := Message{
				Type:      "welcome",
				Data:      "WebSocket连接成功",
				Timestamp: time.Now().Unix(),
			}
			if data, err := json.Marshal(welcome); err == nil {
				select {
				case client.send <- data:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}

		case client := <-h.unregister:
			h.mu.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
				log.Printf("客户端断开: %s", client.conn.RemoteAddr())
			}
			h.mu.Unlock()

		case message := <-h.broadcast:
			h.mu.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}
			h.mu.RUnlock()
		}
	}
}

// startSystemInfoBroadcast 启动系统信息广播
func (h *Hub) startSystemInfoBroadcast() {
	ticker := time.NewTicker(5 * time.Second) // 每5秒推送一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.broadcastSystemInfo()
		}
	}
}

// broadcastSystemInfo 广播系统信息
func (h *Hub) broadcastSystemInfo() {
	systemInfo, err := h.systemMonitor.GetSystemInfo()
	if err != nil {
		log.Printf("获取系统信息失败: %v", err)
		return
	}

	message := Message{
		Type:      "system_info",
		Data:      systemInfo,
		Timestamp: time.Now().Unix(),
	}

	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化系统信息失败: %v", err)
		return
	}

	select {
	case h.broadcast <- data:
	default:
		// 广播通道满了，跳过这次推送
	}
}

// BroadcastMessage 广播自定义消息
func (h *Hub) BroadcastMessage(msgType string, data interface{}) {
	message := Message{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化消息失败: %v", err)
		return
	}

	select {
	case h.broadcast <- jsonData:
	default:
		log.Println("广播通道满，消息被丢弃")
	}
}

// GetClientCount 获取连接的客户端数量
func (h *Hub) GetClientCount() int {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return len(h.clients)
}

// Message WebSocket消息结构
type Message struct {
	Type      string      `json:"type"`      // 消息类型
	Data      interface{} `json:"data"`      // 消息数据
	Timestamp int64       `json:"timestamp"` // 时间戳
}

// WebSocket升级器配置
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源（生产环境应该限制）
		return true
	},
}

// ServeWS 处理WebSocket连接请求
func (h *Hub) ServeWS(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	client := &Client{
		hub:  h,
		conn: conn,
		send: make(chan []byte, 256),
	}

	client.hub.register <- client

	// 启动客户端的读写协程
	go client.writePump()
	go client.readPump()
}

// BroadcastTestResult 广播测试结果
func (h *Hub) BroadcastTestResult(result interface{}) {
	h.BroadcastMessage("test_result", result)
}

// BroadcastAlert 广播告警信息
func (h *Hub) BroadcastAlert(alert interface{}) {
	h.BroadcastMessage("alert", alert)
}

// BroadcastServerUpdate 广播服务器更新
func (h *Hub) BroadcastServerUpdate(server interface{}) {
	h.BroadcastMessage("server_update", server)
}

// Stop 停止Hub
func (h *Hub) Stop() {
	h.mu.Lock()
	defer h.mu.Unlock()

	if !h.running {
		return
	}

	h.running = false

	// 关闭所有客户端连接
	for client := range h.clients {
		close(client.send)
		client.conn.Close()
	}

	log.Println("WebSocket Hub 已停止")
}
