package main

import (
	"flag"
	"fmt"
	"os"
	"server-monitor/internal/config"
	"strings" // Added import for strings.Split
)

func main() {
	// 定义命令行参数
	isServer := flag.Bool("s", false, "以服务器模式运行")
	isClient := flag.Bool("c", false, "以客户端模式运行")

	// 服务端参数
	serverPort := flag.Int("p", 8443, "API监听端口（服务端）")
	serverPassword := flag.String("pw", "", "加密密码（服务端）")
	webPort := flag.Int("web-port", 8080, "Web界面端口（可选，默认8080）")
	dbPath := flag.String("db", "./data/monitor.db", "数据库路径（可选）")
	logLevel := flag.String("log-level", "info", "日志级别（可选）")
	retention := flag.Int("retention", 30, "数据保留天数（可选）")
	dataDir := flag.String("data-dir", "./data", "数据目录（可选）")

	// 客户端参数
	clientName := flag.String("n", "", "服务器名称（客户端）")
	serverIP := flag.String("ip", "", "服务端IP（客户端）")
	clientPassword := flag.String("pw-client", "", "加密密码（客户端，与服务端相同）") // 客户端密码使用不同的flag名以避免冲突
	description := flag.String("desc", "", "服务器描述（可选，客户端）")
	tags := flag.String("tags", "", "标签，逗号分隔（可选，客户端）")
	interval := flag.Int("interval", 30, "监控间隔秒数（可选，客户端，默认30）")

	flag.Parse()

	// 检查模式参数
	if *isServer && *isClient {
		fmt.Println("错误：不能同时指定服务端和客户端模式。")
		os.Exit(1)
	}
	if !*isServer && !*isClient {
		fmt.Println("错误：必须指定服务端（-s）或客户端（-c）模式。")
		os.Exit(1)
	}

	cfg := config.NewManager("") // 使用默认配置文件路径，或者后续从命令行参数中获取

	// 根据模式设置配置
	if *isServer {
		fmt.Println("以服务端模式运行...")
		// 验证服务端必需参数
		if *serverPassword == "" {
			fmt.Println("错误：服务端模式下加密密码（-pw）是必需的。")
			os.Exit(1)
		}

		// 设置服务端配置
		cfg.Get().System.Mode = "server"
		cfg.Get().Peer.Password = *serverPassword // 使用PeerConfig.Password作为共享密码
		cfg.Get().API.Port = *serverPort
		cfg.Get().Web.Port = *webPort
		cfg.Get().Database.Path = *dbPath
		cfg.Get().Log.Level = *logLevel
		cfg.Get().System.DataDir = *dataDir
		cfg.Get().Database.RetentionDays = *retention // 设置数据保留天数

	} else if *isClient {
		fmt.Println("以客户端模式运行...")
		// 验证客户端必需参数
		if *clientName == "" || *serverIP == "" || *serverPort == 0 || *clientPassword == "" {
			fmt.Println("错误：客户端模式下服务器名称（-n）、服务端IP（-ip）、服务端端口（-p）和加密密码（-pw-client）是必需的。")
			os.Exit(1)
		}

		// 设置客户端配置
		cfg.Get().System.Mode = "client"
		cfg.Get().System.Name = *clientName // 将客户端名称设置到SystemConfig.Name
		cfg.Get().Peer.IP = *serverIP
		cfg.Get().Peer.Port = *serverPort
		cfg.Get().Peer.Password = *clientPassword
		cfg.Get().System.Description = *description       // 设置服务器描述
		cfg.Get().System.Tags = strings.Split(*tags, ",") // 设置标签
		cfg.Get().System.Interval = *interval             // 设置监控间隔
		cfg.Get().Log.Level = *logLevel                   // 客户端也应有日志级别
	}

	// 加载并验证配置
	if err := cfg.Load(); err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 假设这里会根据模式启动不同的服务
	fmt.Printf("当前配置模式: %s\n", cfg.Get().System.Mode)
	fmt.Printf("日志级别: %s\n", cfg.Get().Log.Level)
	// 继续其他初始化和启动逻辑...
}
