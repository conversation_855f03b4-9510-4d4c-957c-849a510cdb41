package database

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	_ "modernc.org/sqlite"
)

// Config 数据库配置
type Config struct {
	DatabasePath     string        `json:"database_path"`      // 数据库文件路径
	MaxOpenConns     int           `json:"max_open_conns"`     // 最大打开连接数
	MaxIdleConns     int           `json:"max_idle_conns"`     // 最大空闲连接数
	ConnMaxLifetime  time.Duration `json:"conn_max_lifetime"`  // 连接最大生存时间
	ConnMaxIdleTime  time.Duration `json:"conn_max_idle_time"` // 连接最大空闲时间
	EnableWAL        bool          `json:"enable_wal"`         // 启用WAL模式
	EnableForeignKey bool          `json:"enable_foreign_key"` // 启用外键约束
	BusyTimeout      time.Duration `json:"busy_timeout"`       // 忙碌超时时间
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		DatabasePath:     "data/monitor.db",
		MaxOpenConns:     25,
		MaxIdleConns:     5,
		ConnMaxLifetime:  time.Hour,
		ConnMaxIdleTime:  time.Minute * 10,
		EnableWAL:        true,
		EnableForeignKey: true,
		BusyTimeout:      time.Second * 30,
	}
}

// Database SQLite数据库管理器
type Database struct {
	db     *sql.DB
	config *Config
	mu     sync.RWMutex
	closed bool
}

// NewDatabase 创建新的数据库实例
func NewDatabase(config *Config) (*Database, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// 确保数据库目录存在
	dbDir := filepath.Dir(config.DatabasePath)
	if err := ensureDir(dbDir); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// 构建连接字符串
	dsn := buildDSN(config)

	// 打开数据库连接
	db, err := sql.Open("sqlite", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(config.MaxOpenConns)
	db.SetMaxIdleConns(config.MaxIdleConns)
	db.SetConnMaxLifetime(config.ConnMaxLifetime)
	db.SetConnMaxIdleTime(config.ConnMaxIdleTime)

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	database := &Database{
		db:     db,
		config: config,
		closed: false,
	}

	// 初始化数据库设置
	if err := database.initializeSettings(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to initialize database settings: %w", err)
	}

	return database, nil
}

// buildDSN 构建数据源名称
func buildDSN(config *Config) string {
	dsn := config.DatabasePath + "?"

	// WAL模式
	if config.EnableWAL {
		dsn += "journal_mode=WAL&"
	}

	// 外键约束
	if config.EnableForeignKey {
		dsn += "foreign_keys=ON&"
	}

	// 忙碌超时
	if config.BusyTimeout > 0 {
		dsn += fmt.Sprintf("busy_timeout=%d&", int(config.BusyTimeout.Milliseconds()))
	}

	// 其他优化设置
	dsn += "synchronous=NORMAL&"
	dsn += "cache_size=-64000&" // 64MB缓存
	dsn += "temp_store=MEMORY&"
	dsn += "mmap_size=268435456" // 256MB内存映射

	return dsn
}

// initializeSettings 初始化数据库设置
func (d *Database) initializeSettings() error {
	settings := []string{
		"PRAGMA journal_mode = WAL",
		"PRAGMA synchronous = NORMAL",
		"PRAGMA cache_size = -64000",
		"PRAGMA temp_store = MEMORY",
		"PRAGMA mmap_size = 268435456",
	}

	if d.config.EnableForeignKey {
		settings = append(settings, "PRAGMA foreign_keys = ON")
	}

	for _, setting := range settings {
		if _, err := d.db.Exec(setting); err != nil {
			return fmt.Errorf("failed to execute setting '%s': %w", setting, err)
		}
	}

	return nil
}

// GetDB 获取数据库连接
func (d *Database) GetDB() *sql.DB {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.db
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.closed {
		return nil
	}

	d.closed = true
	return d.db.Close()
}

// IsClosed 检查数据库是否已关闭
func (d *Database) IsClosed() bool {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.closed
}

// Ping 测试数据库连接
func (d *Database) Ping() error {
	d.mu.RLock()
	defer d.mu.RUnlock()

	if d.closed {
		return ErrDatabaseClosed
	}

	return d.db.Ping()
}

// BeginTx 开始事务
func (d *Database) BeginTx() (*sql.Tx, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	if d.closed {
		return nil, ErrDatabaseClosed
	}

	return d.db.Begin()
}

// Stats 获取数据库统计信息
func (d *Database) Stats() sql.DBStats {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.db.Stats()
}

// GetConfig 获取数据库配置
func (d *Database) GetConfig() *Config {
	return d.config
}

// ensureDir 确保目录存在
func ensureDir(dir string) error {
	return os.MkdirAll(dir, 0755)
}
