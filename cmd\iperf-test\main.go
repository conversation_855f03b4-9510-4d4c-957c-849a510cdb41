package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/iperf"
)

func main() {
	var (
		configFile = flag.String("config", "./configs/config.yaml", "配置文件路径")
		testMode   = flag.String("mode", "schedule", "测试模式: schedule(调度), single(单次), check(检查)")
		serverIP   = flag.String("server", "", "服务器IP地址(单次测试用)")
		serverPort = flag.Int("port", 5201, "服务器端口(单次测试用)")
	)
	flag.Parse()

	fmt.Printf("iPerf3测试程序\n")
	fmt.Printf("配置文件: %s\n", *configFile)
	fmt.Printf("测试模式: %s\n", *testMode)
	fmt.Println("---")

	// 检查iPerf3是否可用
	if !iperf.IsAvailable() {
		log.Fatal("iPerf3未安装或不在PATH中，请先安装iPerf3")
	}

	version, err := iperf.GetVersion()
	if err != nil {
		log.Printf("警告: 无法获取iPerf3版本: %v", err)
	} else {
		fmt.Printf("iPerf3版本: %s", version)
	}

	// 加载配置
	configManager := config.NewManager(*configFile)
	if err := configManager.Load(); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	cfg := configManager.Get()
	fmt.Printf("配置加载成功\n")

	switch *testMode {
	case "check":
		runCheckMode()
	case "single":
		runSingleTest(*serverIP, *serverPort, cfg)
	case "schedule":
		runScheduleMode(cfg)
	default:
		log.Fatalf("未知的测试模式: %s", *testMode)
	}
}

// runCheckMode 检查模式
func runCheckMode() {
	fmt.Println("=== iPerf3环境检查 ===")
	
	if iperf.IsAvailable() {
		fmt.Println("✅ iPerf3已安装")
		
		version, err := iperf.GetVersion()
		if err != nil {
			fmt.Printf("❌ 获取版本失败: %v\n", err)
		} else {
			fmt.Printf("📋 版本信息:\n%s", version)
		}
	} else {
		fmt.Println("❌ iPerf3未安装")
		fmt.Println("请安装iPerf3:")
		fmt.Println("  Windows: 从 https://iperf.fr/iperf-download.php 下载")
		fmt.Println("  Linux: sudo apt-get install iperf3")
		fmt.Println("  macOS: brew install iperf3")
	}
}

// runSingleTest 单次测试模式
func runSingleTest(serverIP string, serverPort int, cfg *config.Config) {
	if serverIP == "" {
		log.Fatal("单次测试模式需要指定服务器IP地址")
	}

	fmt.Printf("=== 单次测试模式 ===\n")
	fmt.Printf("目标服务器: %s:%d\n", serverIP, serverPort)

	// 创建客户端配置
	clientConfig := &iperf.ClientConfig{
		ServerHost: serverIP,
		ServerPort: serverPort,
		Duration:   cfg.IPerf.TestDuration,
		Parallel:   cfg.IPerf.ParallelStreams,
		Reverse:    false,
	}

	client := iperf.NewClient(clientConfig)

	// 测试连接
	fmt.Println("测试连接...")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err := client.TestConnection(ctx)
	if err != nil {
		log.Fatalf("连接测试失败: %v", err)
	}
	fmt.Println("✅ 连接正常")

	// 执行上传测试
	fmt.Println("执行上传测试...")
	ctx, cancel = context.WithTimeout(context.Background(), 
		time.Duration(cfg.IPerf.TestTimeout)*time.Second)
	defer cancel()

	uploadResult, err := client.Run(ctx)
	if err != nil {
		log.Fatalf("上传测试失败: %v", err)
	}

	// 执行下载测试
	fmt.Println("执行下载测试...")
	clientConfig.Reverse = true
	downloadClient := iperf.NewClient(clientConfig)

	ctx, cancel = context.WithTimeout(context.Background(), 
		time.Duration(cfg.IPerf.TestTimeout)*time.Second)
	defer cancel()

	downloadResult, err := downloadClient.Run(ctx)
	if err != nil {
		log.Fatalf("下载测试失败: %v", err)
	}

	// 显示结果
	fmt.Println("\n=== 测试结果 ===")
	fmt.Printf("上传速度: %.2f Mbps\n", uploadResult.Bandwidth)
	fmt.Printf("下载速度: %.2f Mbps\n", downloadResult.Bandwidth)
	fmt.Printf("平均延迟: %.2f ms\n", (uploadResult.Latency+downloadResult.Latency)/2)
	fmt.Printf("平均抖动: %.2f ms\n", (uploadResult.Jitter+downloadResult.Jitter)/2)
	fmt.Printf("平均丢包率: %.2f%%\n", (uploadResult.PacketLoss+downloadResult.PacketLoss)/2)
}

// runScheduleMode 调度模式
func runScheduleMode(cfg *config.Config) {
	fmt.Println("=== 调度模式 ===")

	// 初始化数据库
	dbConfig := &database.Config{
		DatabasePath:     cfg.Database.Path,
		MaxOpenConns:     cfg.Database.MaxOpenConns,
		MaxIdleConns:     cfg.Database.MaxIdleConns,
		ConnMaxLifetime:  cfg.Database.ConnMaxLifetime,
		ConnMaxIdleTime:  cfg.Database.ConnMaxIdleTime,
		EnableWAL:        cfg.Database.EnableWAL,
		EnableForeignKey: cfg.Database.EnableForeignKey,
		BusyTimeout:      cfg.Database.BusyTimeout,
	}

	db, repo, err := database.InitDatabase(dbConfig)
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}
	defer db.Close()

	fmt.Printf("数据库初始化成功\n")

	// 初始化测试数据
	if err := database.InitTestServers(repo); err != nil {
		log.Printf("警告: 测试数据初始化失败: %v", err)
	} else {
		fmt.Printf("测试数据初始化成功\n")
	}

	// 创建调度器
	scheduler := iperf.NewScheduler(cfg, repo)

	// 设置信号处理
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// 启动调度器
	if err := scheduler.Start(); err != nil {
		log.Fatalf("启动调度器失败: %v", err)
	}

	fmt.Printf("调度器已启动，测试间隔: %d分钟\n", cfg.IPerf.TestInterval)
	fmt.Printf("最大并发测试: %d\n", cfg.IPerf.MaxConcurrentTests)
	fmt.Printf("测试超时: %d秒\n", cfg.IPerf.TestTimeout)

	// 显示统计信息
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				showStats(scheduler)
			case <-sigCh:
				return
			}
		}
	}()

	fmt.Println("调度器运行中，按 Ctrl+C 停止...")

	// 等待信号
	<-sigCh
	fmt.Println("\n收到停止信号，正在停止调度器...")

	// 停止调度器
	scheduler.Stop()
	fmt.Println("调度器已停止")
}

// showStats 显示统计信息
func showStats(scheduler *iperf.Scheduler) {
	stats, err := scheduler.GetTestStats()
	if err != nil {
		log.Printf("获取统计信息失败: %v", err)
		return
	}

	fmt.Println("\n=== 测试统计 (最近24小时) ===")
	fmt.Printf("总测试次数: %d\n", stats.TotalTests)
	fmt.Printf("成功测试: %d\n", stats.SuccessTests)
	fmt.Printf("失败测试: %d\n", stats.FailedTests)
	fmt.Printf("成功率: %.1f%%\n", stats.SuccessRate)
	fmt.Printf("平均带宽: %.2f Mbps\n", stats.AvgBandwidth)
	fmt.Printf("最后更新: %s\n", stats.LastUpdateTime.Format("2006-01-02 15:04:05"))
	fmt.Println("---")
}
