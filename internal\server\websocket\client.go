package websocket

import (
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

const (
	// 允许向对等方写入消息的时间。(Time allowed to write a message to the peer.)
	writeWait = 10 * time.Second

	// 允许从对等方读取下一个 pong 消息的时间。(Time allowed to read the next pong message from the peer.)
	pongWait = 60 * time.Second

	// 以此周期向对等方发送 ping。必须小于 pongWait。(Send pings to peer with this period. Must be less than pongWait.)
	pingPeriod = (pongWait * 9) / 10

	// 允许从对等方接收的最大消息大小。(Maximum message size allowed from peer.)
	maxMessageSize = 512
)

var (
	newline = []byte{'\n'}
	space   = []byte{' '}
)

// Client 是 WebSocket 连接与 Hub 之间的中间件。(Client is the middleware between the WebSocket connection and the Hub.)
type Client struct {
	hub *Hub

	// WebSocket 连接。(The WebSocket connection.)
	conn *websocket.Conn

	// 出站消息的缓冲通道。(Buffered channel of outbound messages.)
	send chan []byte
}

// readPump 将消息从 WebSocket 连接泵送到 Hub。(readPump pumps messages from the WebSocket connection to the Hub.)
//
// 应用程序在 goroutine 中运行 readPump 以防止阻塞调用方。(The application runs readPump in a goroutine to prevent blocking the caller.)
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()
	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error { c.conn.SetReadDeadline(time.Now().Add(pongWait)); return nil })
	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("error: %v", err)
			}
			break
		}
		// 在实际应用中，在此处处理消息。(In real applications, process messages here.)
		// 目前，我们只将其回显或发送到 Hub 的广播通道。(Currently, we just echo it back or send it to the Hub's broadcast channel.)
		c.hub.broadcast <- message
	}
}

// writePump 将消息从 Hub 泵送到 WebSocket 连接。(writePump pumps messages from the Hub to the WebSocket connection.)
//
// 为每个连接启动一个运行 writePump 的 goroutine。(A goroutine running writePump is started for each connection.)
// 应用程序通过在此 goroutine 中执行所有写入来确保每个连接最多只有一个写入器。(The application ensures that there is at most one writer per connection by executing all writes in this goroutine.)
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()
	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// Hub 关闭了通道。(The Hub closed the channel.)
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 将排队的消息添加到当前的 WebSocket 消息中。(Add queued messages to the current WebSocket message.)
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write(newline)
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}
		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// ServeWs 处理来自对等方的 WebSocket 请求。(ServeWs handles WebSocket requests from the peer.)
func ServeWs(hub *Hub, w http.ResponseWriter, r *http.Request) {
	conn, err := websocket.Upgrade(w, r, nil, 1024, 1024)
	if err != nil {
		log.Println(err)
		return
	}
	client := &Client{hub: hub, conn: conn, send: make(chan []byte, 256)}
	client.hub.register <- client

	// 允许通过在新的 goroutine 中完成所有工作来收集调用方引用的内存。(Allows collecting memory referenced by the caller by doing all work in a new goroutine.)
	go client.writePump()
	go client.readPump()
}
