package api

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/monitor"
)

func setupTestServer(t *testing.T) (*Server, database.Repository, func()) {
	// 创建测试配置
	cfg := &config.Config{
		System: config.SystemConfig{
			Environment: "testing",
			Version:     "test",
		},
		API: config.APIConfig{
			Prefix:  "/api/v1",
			Timeout: 30 * time.Second,
		},
	}

	// 创建内存数据库
	dbConfig := &database.Config{
		DatabasePath: ":memory:",
		MaxOpenConns: 1,
		MaxIdleConns: 1,
	}

	db, repo, err := database.InitDatabase(dbConfig)
	if err != nil {
		t.Fatalf("Failed to init test database: %v", err)
	}

	// 创建系统监控器
	sysMon := monitor.NewSystemMonitor(time.Second)

	// 创建API服务器
	server := NewServer(cfg, repo, sysMon)

	cleanup := func() {
		db.Close()
	}

	return server, repo, cleanup
}

func TestNewServer(t *testing.T) {
	server, _, cleanup := setupTestServer(t)
	defer cleanup()

	if server == nil {
		t.Fatal("NewServer returned nil")
	}

	if server.config == nil {
		t.Error("Server config should not be nil")
	}

	if server.repository == nil {
		t.Error("Server repository should not be nil")
	}

	if server.systemMonitor == nil {
		t.Error("Server system monitor should not be nil")
	}

	if server.router == nil {
		t.Error("Server router should not be nil")
	}
}

func TestHealthCheck(t *testing.T) {
	server, _, cleanup := setupTestServer(t)
	defer cleanup()

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/health", nil)
	server.router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response Response
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}

	if response.Data == nil {
		t.Error("Expected data to be present")
	}
}

func TestGetSystemInfo(t *testing.T) {
	server, _, cleanup := setupTestServer(t)
	defer cleanup()

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/system/info", nil)
	server.router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response Response
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}

	if response.Data == nil {
		t.Error("Expected system info data to be present")
	}
}

func TestListServers(t *testing.T) {
	server, repo, cleanup := setupTestServer(t)
	defer cleanup()

	// 创建测试服务器
	testServer := &database.Server{
		Name:     "test-server",
		IP:       "*************",
		Port:     5201,
		Location: "Test Location",
		IsActive: true,
	}

	if err := repo.CreateServer(testServer); err != nil {
		t.Fatalf("Failed to create test server: %v", err)
	}

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/servers", nil)
	server.router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response Response
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}

	// 验证返回的服务器列表
	servers, ok := response.Data.([]interface{})
	if !ok {
		t.Error("Expected data to be a slice")
	}

	if len(servers) != 1 {
		t.Errorf("Expected 1 server, got %d", len(servers))
	}
}

func TestCreateServer(t *testing.T) {
	server, _, cleanup := setupTestServer(t)
	defer cleanup()

	newServer := database.Server{
		Name:     "new-server",
		IP:       "*************",
		Port:     5201,
		Location: "New Location",
		IsActive: true,
	}

	jsonData, _ := json.Marshal(newServer)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/servers", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	server.router.ServeHTTP(w, req)

	if w.Code != http.StatusCreated {
		t.Errorf("Expected status %d, got %d", http.StatusCreated, w.Code)
	}

	var response Response
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}
}

func TestGetServer(t *testing.T) {
	server, repo, cleanup := setupTestServer(t)
	defer cleanup()

	// 创建测试服务器
	testServer := &database.Server{
		Name:     "test-server",
		IP:       "*************",
		Port:     5201,
		Location: "Test Location",
		IsActive: true,
	}

	if err := repo.CreateServer(testServer); err != nil {
		t.Fatalf("Failed to create test server: %v", err)
	}

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/servers/"+strconv.Itoa(testServer.ID), nil)
	server.router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response Response
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}
}

func TestUpdateServer(t *testing.T) {
	server, repo, cleanup := setupTestServer(t)
	defer cleanup()

	// 创建测试服务器
	testServer := &database.Server{
		Name:     "test-server",
		IP:       "*************",
		Port:     5201,
		Location: "Test Location",
		IsActive: true,
	}

	if err := repo.CreateServer(testServer); err != nil {
		t.Fatalf("Failed to create test server: %v", err)
	}

	// 更新服务器信息
	testServer.Name = "updated-server"
	testServer.Location = "Updated Location"

	jsonData, _ := json.Marshal(testServer)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("PUT", "/api/v1/servers/"+testServer.ID, bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	server.router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response Response
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}
}

func TestDeleteServer(t *testing.T) {
	server, repo, cleanup := setupTestServer(t)
	defer cleanup()

	// 创建测试服务器
	testServer := &database.Server{
		Name:     "test-server",
		IP:       "*************",
		Port:     5201,
		Location: "Test Location",
		Enabled:  true,
	}

	if err := repo.CreateServer(testServer); err != nil {
		t.Fatalf("Failed to create test server: %v", err)
	}

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("DELETE", "/api/v1/servers/"+testServer.ID, nil)
	server.router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response Response
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}
}

func TestGetStatsSummary(t *testing.T) {
	server, repo, cleanup := setupTestServer(t)
	defer cleanup()

	// 创建测试服务器
	testServer := &database.Server{
		Name:     "test-server",
		IP:       "*************",
		Port:     5201,
		Location: "Test Location",
		Enabled:  true,
	}

	if err := repo.CreateServer(testServer); err != nil {
		t.Fatalf("Failed to create test server: %v", err)
	}

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/stats/summary", nil)
	server.router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response Response
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}

	if response.Data == nil {
		t.Error("Expected stats data to be present")
	}
}

func TestInvalidRoutes(t *testing.T) {
	server, _, cleanup := setupTestServer(t)
	defer cleanup()

	testCases := []struct {
		method string
		path   string
		status int
	}{
		{"GET", "/api/v1/nonexistent", http.StatusNotFound},
		{"POST", "/api/v1/servers/invalid", http.StatusNotFound},
		{"GET", "/api/v1/servers/nonexistent-id", http.StatusNotFound},
	}

	for _, tc := range testCases {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(tc.method, tc.path, nil)
		server.router.ServeHTTP(w, req)

		if w.Code != tc.status {
			t.Errorf("Expected status %d for %s %s, got %d", tc.status, tc.method, tc.path, w.Code)
		}
	}
}
