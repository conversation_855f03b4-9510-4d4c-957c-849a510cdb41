package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"server-monitor/internal/manager"
)

func main() {
	var (
		action      = flag.String("action", "list", "操作类型: list, get, start, stop, restart, enable, disable, logs")
		serviceName = flag.String("name", "", "服务名称")
		lines       = flag.Int("lines", 50, "日志行数")
		timeout     = flag.Int("timeout", 30, "操作超时时间(秒)")
		format      = flag.String("format", "table", "输出格式: table, json")
	)
	flag.Parse()

	fmt.Printf("服务管理测试程序\n")
	fmt.Printf("操作: %s\n", *action)
	if *serviceName != "" {
		fmt.Printf("服务: %s\n", *serviceName)
	}
	fmt.Println("---")

	// 创建服务管理器
	serviceManager := manager.NewManager()

	// 设置超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(*timeout)*time.Second)
	defer cancel()

	switch *action {
	case "list":
		listServices(ctx, serviceManager, *format)
	case "get":
		if *serviceName == "" {
			log.Fatal("请指定服务名称 --name")
		}
		getService(ctx, serviceManager, *serviceName, *format)
	case "start":
		if *serviceName == "" {
			log.Fatal("请指定服务名称 --name")
		}
		startService(ctx, serviceManager, *serviceName)
	case "stop":
		if *serviceName == "" {
			log.Fatal("请指定服务名称 --name")
		}
		stopService(ctx, serviceManager, *serviceName)
	case "restart":
		if *serviceName == "" {
			log.Fatal("请指定服务名称 --name")
		}
		restartService(ctx, serviceManager, *serviceName)
	case "enable":
		if *serviceName == "" {
			log.Fatal("请指定服务名称 --name")
		}
		enableService(ctx, serviceManager, *serviceName)
	case "disable":
		if *serviceName == "" {
			log.Fatal("请指定服务名称 --name")
		}
		disableService(ctx, serviceManager, *serviceName)
	case "logs":
		if *serviceName == "" {
			log.Fatal("请指定服务名称 --name")
		}
		getServiceLogs(ctx, serviceManager, *serviceName, *lines)
	default:
		log.Fatalf("未知操作: %s", *action)
	}
}

// listServices 列出所有服务
func listServices(ctx context.Context, manager *manager.Manager, format string) {
	fmt.Println("=== 服务列表 ===")

	services, err := manager.ListServices(ctx)
	if err != nil {
		log.Fatalf("获取服务列表失败: %v", err)
	}

	if len(services) == 0 {
		fmt.Println("未找到任何服务")
		return
	}

	switch format {
	case "json":
		outputJSON(services)
	default:
		outputTable(services)
	}

	fmt.Printf("\n总计: %d 个服务\n", len(services))
}

// getService 获取服务详情
func getService(ctx context.Context, manager *manager.Manager, name, format string) {
	fmt.Printf("=== 服务详情: %s ===\n", name)

	service, err := manager.GetService(ctx, name)
	if err != nil {
		log.Fatalf("获取服务详情失败: %v", err)
	}

	switch format {
	case "json":
		outputJSON(service)
	default:
		fmt.Printf("名称: %s\n", service.Name)
		fmt.Printf("显示名称: %s\n", service.DisplayName)
		fmt.Printf("状态: %s\n", service.Status)
		fmt.Printf("类型: %s\n", service.Type)
		if service.PID > 0 {
			fmt.Printf("进程ID: %d\n", service.PID)
		}
		if service.StartTime != nil {
			fmt.Printf("启动时间: %s\n", service.StartTime.Format("2006-01-02 15:04:05"))
		}
		if service.Description != "" {
			fmt.Printf("描述: %s\n", service.Description)
		}
	}
}

// startService 启动服务
func startService(ctx context.Context, manager *manager.Manager, name string) {
	fmt.Printf("=== 启动服务: %s ===\n", name)

	if !manager.ServiceExists(ctx, name) {
		log.Fatalf("服务不存在: %s", name)
	}

	err := manager.StartService(ctx, name)
	if err != nil {
		log.Fatalf("启动服务失败: %v", err)
	}

	fmt.Printf("✅ 服务 %s 启动成功\n", name)

	// 等待一下再检查状态
	time.Sleep(2 * time.Second)
	checkServiceStatus(ctx, manager, name)
}

// stopService 停止服务
func stopService(ctx context.Context, manager *manager.Manager, name string) {
	fmt.Printf("=== 停止服务: %s ===\n", name)

	if !manager.ServiceExists(ctx, name) {
		log.Fatalf("服务不存在: %s", name)
	}

	err := manager.StopService(ctx, name)
	if err != nil {
		log.Fatalf("停止服务失败: %v", err)
	}

	fmt.Printf("✅ 服务 %s 停止成功\n", name)

	// 等待一下再检查状态
	time.Sleep(2 * time.Second)
	checkServiceStatus(ctx, manager, name)
}

// restartService 重启服务
func restartService(ctx context.Context, manager *manager.Manager, name string) {
	fmt.Printf("=== 重启服务: %s ===\n", name)

	if !manager.ServiceExists(ctx, name) {
		log.Fatalf("服务不存在: %s", name)
	}

	err := manager.RestartService(ctx, name)
	if err != nil {
		log.Fatalf("重启服务失败: %v", err)
	}

	fmt.Printf("✅ 服务 %s 重启成功\n", name)

	// 等待一下再检查状态
	time.Sleep(3 * time.Second)
	checkServiceStatus(ctx, manager, name)
}

// enableService 启用服务
func enableService(ctx context.Context, manager *manager.Manager, name string) {
	fmt.Printf("=== 启用服务: %s ===\n", name)

	if !manager.ServiceExists(ctx, name) {
		log.Fatalf("服务不存在: %s", name)
	}

	err := manager.EnableService(ctx, name)
	if err != nil {
		log.Fatalf("启用服务失败: %v", err)
	}

	fmt.Printf("✅ 服务 %s 启用成功\n", name)
}

// disableService 禁用服务
func disableService(ctx context.Context, manager *manager.Manager, name string) {
	fmt.Printf("=== 禁用服务: %s ===\n", name)

	if !manager.ServiceExists(ctx, name) {
		log.Fatalf("服务不存在: %s", name)
	}

	err := manager.DisableService(ctx, name)
	if err != nil {
		log.Fatalf("禁用服务失败: %v", err)
	}

	fmt.Printf("✅ 服务 %s 禁用成功\n", name)
}

// getServiceLogs 获取服务日志
func getServiceLogs(ctx context.Context, manager *manager.Manager, name string, lines int) {
	fmt.Printf("=== 服务日志: %s (最近 %d 行) ===\n", name, lines)

	if !manager.ServiceExists(ctx, name) {
		log.Fatalf("服务不存在: %s", name)
	}

	logs, err := manager.GetServiceLogs(ctx, name, lines)
	if err != nil {
		log.Fatalf("获取服务日志失败: %v", err)
	}

	if len(logs) == 0 {
		fmt.Println("暂无日志")
		return
	}

	for _, logLine := range logs {
		fmt.Println(logLine)
	}

	fmt.Printf("\n显示了 %d 行日志\n", len(logs))
}

// checkServiceStatus 检查服务状态
func checkServiceStatus(ctx context.Context, manager *manager.Manager, name string) {
	service, err := manager.GetService(ctx, name)
	if err != nil {
		fmt.Printf("⚠️  无法获取服务状态: %v\n", err)
		return
	}

	fmt.Printf("当前状态: %s", service.Status)
	if service.PID > 0 {
		fmt.Printf(" (PID: %d)", service.PID)
	}
	fmt.Println()
}

// outputTable 表格输出
func outputTable(services []*manager.Service) {
	// 表头
	fmt.Printf("%-30s %-20s %-10s %-10s %-s\n", "名称", "显示名称", "状态", "类型", "描述")
	fmt.Println(strings.Repeat("-", 100))

	// 数据行
	for _, service := range services {
		displayName := service.DisplayName
		if len(displayName) > 20 {
			displayName = displayName[:17] + "..."
		}

		description := service.Description
		if len(description) > 30 {
			description = description[:27] + "..."
		}

		fmt.Printf("%-30s %-20s %-10s %-10s %-s\n",
			service.Name,
			displayName,
			service.Status,
			service.Type,
			description,
		)
	}
}

// outputJSON JSON输出
func outputJSON(data interface{}) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		log.Printf("JSON序列化失败: %v", err)
		return
	}
	fmt.Println(string(jsonData))
}
