{{define "content"}}
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value">{{.totalServers}}</div>
        <div class="stat-label">总服务器数</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{.activeServers}}</div>
        <div class="stat-label">活跃服务器</div>
    </div>
    {{if .systemInfo}}
    <div class="stat-card">
        <div class="stat-value">{{printf "%.1f%%" .systemInfo.CPUUsage}}</div>
        <div class="stat-label">当前CPU使用率</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{printf "%.1f%%" .systemInfo.MemoryUsage}}</div>
        <div class="stat-label">当前内存使用率</div>
    </div>
    {{end}}
</div>

<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <h2>📊 服务器统计</h2>
        <button class="refresh-btn" onclick="refreshStatsData()">刷新数据</button>
    </div>
    
    {{if .servers}}
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
        <!-- 按地区分布 -->
        <div>
            <h3>🌍 地区分布</h3>
            <div id="location-distribution" style="margin-top: 1rem;">
                <!-- 将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 按提供商分布 -->
        <div>
            <h3>🏢 提供商分布</h3>
            <div id="provider-distribution" style="margin-top: 1rem;">
                <!-- 将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 按优先级分布 -->
        <div>
            <h3>⭐ 优先级分布</h3>
            <div id="priority-distribution" style="margin-top: 1rem;">
                <!-- 将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 服务器数据将通过AJAX获取 -->
    {{else}}
    <div style="text-align: center; padding: 2rem; color: #666;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
        <p>暂无统计数据</p>
    </div>
    {{end}}
</div>

{{if .systemInfo}}
<div class="card">
    <h2>💻 系统资源统计</h2>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
        <div>
            <h3>处理器信息</h3>
            <div style="margin-top: 1rem;">
                <div style="display: flex; justify-content: space-between; margin: 0.5rem 0;">
                    <span>使用率:</span>
                    <span><strong>{{printf "%.1f%%" .systemInfo.CPUUsage}}</strong></span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill {{if ge .systemInfo.CPUUsage 90}}danger{{else if ge .systemInfo.CPUUsage 70}}warning{{end}}" 
                         style="width: {{.systemInfo.CPUUsage}}%"></div>
                </div>
            </div>
        </div>
        
        <div>
            <h3>内存信息</h3>
            <div style="margin-top: 1rem;">
                <div style="display: flex; justify-content: space-between; margin: 0.5rem 0;">
                    <span>使用率:</span>
                    <span><strong>{{printf "%.1f%%" .systemInfo.MemoryUsage}}</strong></span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill {{if ge .systemInfo.MemoryUsage 90}}danger{{else if ge .systemInfo.MemoryUsage 70}}warning{{end}}" 
                         style="width: {{.systemInfo.MemoryUsage}}%"></div>
                </div>
                <div style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;">
                    {{printf "%.0f MB / %.0f MB" .systemInfo.MemoryUsed .systemInfo.MemoryTotal}}
                </div>
            </div>
        </div>
        
        <div>
            <h3>磁盘信息</h3>
            <div style="margin-top: 1rem;">
                <div style="display: flex; justify-content: space-between; margin: 0.5rem 0;">
                    <span>使用率:</span>
                    <span><strong>{{printf "%.1f%%" .systemInfo.DiskUsage}}</strong></span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill {{if ge .systemInfo.DiskUsage 90}}danger{{else if ge .systemInfo.DiskUsage 70}}warning{{end}}" 
                         style="width: {{.systemInfo.DiskUsage}}%"></div>
                </div>
                <div style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;">
                    {{printf "%.0f MB / %.0f MB" .systemInfo.DiskUsed .systemInfo.DiskTotal}}
                </div>
            </div>
        </div>
        
        <div>
            <h3>网络流量</h3>
            <div style="margin-top: 1rem;">
                <div style="display: flex; justify-content: space-between; margin: 0.5rem 0;">
                    <span>接收:</span>
                    <span><strong>{{printf "%.0f MB" .systemInfo.NetworkRx}}</strong></span>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 0.5rem 0;">
                    <span>发送:</span>
                    <span><strong>{{printf "%.0f MB" .systemInfo.NetworkTx}}</strong></span>
                </div>
                <div style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;">
                    运行时间: {{.systemInfo.Uptime}} 秒
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

<div class="card">
    <h2>📈 性能测试统计</h2>
    <div style="text-align: center; padding: 2rem; color: #666;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">⚡</div>
        <p>性能测试统计功能正在开发中...</p>
        <p style="font-size: 0.9rem; margin-top: 0.5rem;">将显示iPerf3测试结果的统计分析</p>
        <div style="margin-top: 1.5rem; display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
            <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px;">
                <div style="font-size: 1.5rem; color: #667eea; margin-bottom: 0.5rem;">--</div>
                <div style="font-size: 0.9rem;">总测试次数</div>
            </div>
            <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px;">
                <div style="font-size: 1.5rem; color: #4CAF50; margin-bottom: 0.5rem;">--</div>
                <div style="font-size: 0.9rem;">成功率</div>
            </div>
            <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px;">
                <div style="font-size: 1.5rem; color: #FF9800; margin-bottom: 0.5rem;">--</div>
                <div style="font-size: 0.9rem;">平均带宽</div>
            </div>
            <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px;">
                <div style="font-size: 1.5rem; color: #9C27B0; margin-bottom: 0.5rem;">--</div>
                <div style="font-size: 0.9rem;">最佳性能</div>
            </div>
        </div>
    </div>
</div>

<div class="timestamp">
    最后更新: <span id="last-update">{{.timestamp}}</span>
</div>

<script>
function refreshStatsData() {
    // 获取服务器数据并更新分布统计
    fetch('/data/servers')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                updateDistributionStats(data.data);
                document.getElementById('last-update').textContent = new Date().toLocaleString('zh-CN');
            }
        })
        .catch(error => {
            console.error('Failed to refresh stats data:', error);
        });
}

function updateDistributionStats(servers) {
    // 统计地区分布
    const locationStats = {};
    const providerStats = {};
    const priorityStats = {};

    servers.forEach(server => {
        // 地区统计
        const location = server.location || '未知';
        locationStats[location] = (locationStats[location] || 0) + 1;

        // 提供商统计
        const provider = server.provider || '未知';
        providerStats[provider] = (providerStats[provider] || 0) + 1;

        // 优先级统计
        const priority = server.priority || 3;
        priorityStats[priority] = (priorityStats[priority] || 0) + 1;
    });

    // 更新显示
    updateDistributionDisplay('location-distribution', locationStats, '#667eea');
    updateDistributionDisplay('provider-distribution', providerStats, '#4CAF50');
    updatePriorityDisplay('priority-distribution', priorityStats);
}

function updateDistributionDisplay(containerId, stats, color) {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = '';

    Object.entries(stats).forEach(([key, count]) => {
        const div = document.createElement('div');
        div.style.cssText = 'display: flex; justify-content: space-between; align-items: center; margin: 0.5rem 0; padding: 0.5rem; background: #f8f9fa; border-radius: 5px;';
        div.innerHTML = `
            <span>${key}</span>
            <span style="background: ${color}; color: white; padding: 0.2rem 0.5rem; border-radius: 3px; font-size: 0.9rem;">${count}</span>
        `;
        container.appendChild(div);
    });
}

function updatePriorityDisplay(containerId, stats) {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = '';

    Object.entries(stats).forEach(([priority, count]) => {
        const div = document.createElement('div');
        div.style.cssText = 'display: flex; justify-content: space-between; align-items: center; margin: 0.5rem 0; padding: 0.5rem; background: #f8f9fa; border-radius: 5px;';

        let color = '#9E9E9E';
        if (priority == 1) color = '#4CAF50';
        else if (priority == 2) color = '#FF9800';

        div.innerHTML = `
            <span>优先级 ${priority}</span>
            <span style="background: ${color}; color: white; padding: 0.2rem 0.5rem; border-radius: 3px; font-size: 0.9rem;">${count}</span>
        `;
        container.appendChild(div);
    });
}

// 页面加载时获取数据
document.addEventListener('DOMContentLoaded', refreshStatsData);

// 每30秒自动刷新数据
setInterval(refreshStatsData, 30000);
</script>
{{end}}

{{template "base.html" .}}
