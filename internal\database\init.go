package database

import (
	"fmt"
	"log"
)

// InitDatabase 初始化数据库
func InitDatabase(config *Config) (*Database, Repository, error) {
	// 创建数据库实例
	db, err := NewDatabase(config)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create database: %w", err)
	}

	// 运行数据库迁移
	migrator := NewMigrator(db)
	if err := migrator.Migrate(); err != nil {
		db.Close()
		return nil, nil, fmt.Errorf("failed to run migrations: %w", err)
	}

	// 创建repository
	repo := NewRepository(db)

	// 初始化默认配置
	if err := initDefaultConfigs(repo); err != nil {
		log.Printf("Warning: failed to initialize default configs: %v", err)
	}

	return db, repo, nil
}

// initDefaultConfigs 初始化默认系统配置
func initDefaultConfigs(repo Repository) error {
	defaultConfigs := map[string]struct {
		value       string
		description string
		configType  string
		isSystem    bool
	}{
		"system.version": {
			value:       "1.0.0",
			description: "系统版本号",
			configType:  ConfigTypeString,
			isSystem:    true,
		},
		"system.name": {
			value:       "Server Monitor",
			description: "系统名称",
			configType:  ConfigTypeString,
			isSystem:    true,
		},
		"test.interval": {
			value:       "3600",
			description: "测试间隔时间（秒）",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"test.duration": {
			value:       "30",
			description: "单次测试持续时间（秒）",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"test.parallel": {
			value:       "4",
			description: "并行测试数量",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"sync.enabled": {
			value:       "true",
			description: "是否启用双机同步",
			configType:  ConfigTypeBool,
			isSystem:    true,
		},
		"sync.interval": {
			value:       "300",
			description: "同步间隔时间（秒）",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"web.port": {
			value:       "8080",
			description: "Web界面端口",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"api.port": {
			value:       "8443",
			description: "API服务端口",
			configType:  ConfigTypeInt,
			isSystem:    true,
		},
		"log.level": {
			value:       "info",
			description: "日志级别",
			configType:  ConfigTypeString,
			isSystem:    true,
		},
	}

	for key, config := range defaultConfigs {
		// 检查配置是否已存在
		existing, err := repo.GetConfig(key)
		if err == ErrRecordNotFound {
			// 配置不存在，创建默认配置
			if err := repo.SetConfig(key, config.value, config.description, config.configType, config.isSystem); err != nil {
				return fmt.Errorf("failed to set default config %s: %w", key, err)
			}
		} else if err != nil {
			return fmt.Errorf("failed to check config %s: %w", key, err)
		} else {
			// 配置已存在，更新描述和类型（但不更新值）
			if err := repo.SetConfig(key, existing.ConfigValue, config.description, config.configType, config.isSystem); err != nil {
				return fmt.Errorf("failed to update config %s: %w", key, err)
			}
		}
	}

	return nil
}

// InitTestServers 初始化测试服务器数据
func InitTestServers(repo Repository) error {
	testServers := []*Server{
		{
			Name:     "server-01",
			IP:       "*************",
			Port:     5201,
			Location: "Beijing",
			Provider: "Alibaba Cloud",
			IsActive: true,
		},
		{
			Name:     "server-02",
			IP:       "*************",
			Port:     5201,
			Location: "Shanghai",
			Provider: "Tencent Cloud",
			IsActive: true,
		},
		{
			Name:     "server-03",
			IP:       "*************",
			Port:     5201,
			Location: "Guangzhou",
			Provider: "Huawei Cloud",
			IsActive: true,
		},
		{
			Name:     "server-04",
			IP:       "*************",
			Port:     5201,
			Location: "Shenzhen",
			Provider: "Baidu Cloud",
			IsActive: true,
		},
		{
			Name:     "server-05",
			IP:       "*************",
			Port:     5201,
			Location: "Hangzhou",
			Provider: "Alibaba Cloud",
			IsActive: true,
		},
		{
			Name:     "server-06",
			IP:       "*************",
			Port:     5201,
			Location: "Nanjing",
			Provider: "Tencent Cloud",
			IsActive: true,
		},
		{
			Name:     "server-07",
			IP:       "*************",
			Port:     5201,
			Location: "Wuhan",
			Provider: "Huawei Cloud",
			IsActive: true,
		},
		{
			Name:     "server-08",
			IP:       "*************",
			Port:     5201,
			Location: "Chengdu",
			Provider: "Baidu Cloud",
			IsActive: true,
		},
		{
			Name:     "server-09",
			IP:       "*************",
			Port:     5201,
			Location: "Xi'an",
			Provider: "Alibaba Cloud",
			IsActive: true,
		},
		{
			Name:     "server-10",
			IP:       "*************",
			Port:     5201,
			Location: "Chongqing",
			Provider: "Tencent Cloud",
			IsActive: true,
		},
		{
			Name:     "server-11",
			IP:       "*************",
			Port:     5201,
			Location: "Tianjin",
			Provider: "Huawei Cloud",
			IsActive: true,
		},
		{
			Name:     "server-12",
			IP:       "*************",
			Port:     5201,
			Location: "Qingdao",
			Provider: "Baidu Cloud",
			IsActive: true,
		},
	}

	for _, server := range testServers {
		// 检查服务器是否已存在
		existing, err := repo.GetServerByName(server.Name)
		if err == ErrRecordNotFound {
			// 服务器不存在，创建新服务器
			if err := repo.CreateServer(server); err != nil {
				return fmt.Errorf("failed to create server %s: %w", server.Name, err)
			}
			log.Printf("Created test server: %s (%s)", server.Name, server.IP)
		} else if err != nil {
			return fmt.Errorf("failed to check server %s: %w", server.Name, err)
		} else {
			log.Printf("Test server already exists: %s (%s)", existing.Name, existing.IP)
		}
	}

	return nil
}
