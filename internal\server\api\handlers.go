package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// HealthCheckHandler handles the health check endpoint.
func HealthCheckHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// ExampleRequest represents the request body for the example endpoint.
type ExampleRequest struct {
	Name  string `json:"name" binding:"required"`
	Value int    `json:"value" binding:"required,gte=0"`
}

// ExampleHandler handles an example endpoint with data validation.
func ExampleHandler(c *gin.Context) {
	var req ExampleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Data received and validated successfully", "data": req})
}
