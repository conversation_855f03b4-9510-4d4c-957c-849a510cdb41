{{define "content"}}
<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <h2>🖥️ 系统信息</h2>
        <button class="refresh-btn" onclick="refreshSystemData()">刷新数据</button>
    </div>
    
    {{if .systemInfo}}
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
        <div>
            <h3>基本信息</h3>
            <table>
                <tr><td><strong>主机名</strong></td><td>{{.systemInfo.Hostname}}</td></tr>
                <tr><td><strong>操作系统</strong></td><td>{{.systemInfo.OS}}</td></tr>
                <tr><td><strong>平台</strong></td><td>{{.systemInfo.Platform}}</td></tr>
                <tr><td><strong>架构</strong></td><td>{{.systemInfo.Architecture}}</td></tr>
                <tr><td><strong>运行时间</strong></td><td>{{.systemInfo.Uptime}} 秒</td></tr>
                {{if gt .systemInfo.LoadAverage 0}}
                <tr><td><strong>负载平均值</strong></td><td>{{printf "%.2f" .systemInfo.LoadAverage}}</td></tr>
                {{end}}
            </table>
        </div>
        
        <div>
            <h3>资源使用情况</h3>
            <div style="margin: 1rem 0;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                    <span><strong>CPU使用率</strong></span>
                    <span id="cpu-usage">{{printf "%.1f%%" .systemInfo.CPUUsage}}</span>
                </div>
                <div class="progress-bar">
                    <div id="cpu-progress" class="progress-fill {{if ge .systemInfo.CPUUsage 90}}danger{{else if ge .systemInfo.CPUUsage 70}}warning{{end}}" 
                         style="width: {{.systemInfo.CPUUsage}}%"></div>
                </div>
            </div>
            
            <div style="margin: 1rem 0;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                    <span><strong>内存使用率</strong></span>
                    <span id="memory-usage">{{printf "%.1f%%" .systemInfo.MemoryUsage}}</span>
                </div>
                <div class="progress-bar">
                    <div id="memory-progress" class="progress-fill {{if ge .systemInfo.MemoryUsage 90}}danger{{else if ge .systemInfo.MemoryUsage 70}}warning{{end}}" 
                         style="width: {{.systemInfo.MemoryUsage}}%"></div>
                </div>
                <div style="font-size: 0.9rem; color: #666; margin-top: 0.25rem;">
                    <span id="memory-details">{{printf "%.0f MB / %.0f MB" .systemInfo.MemoryUsed .systemInfo.MemoryTotal}}</span>
                </div>
            </div>
            
            <div style="margin: 1rem 0;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                    <span><strong>磁盘使用率</strong></span>
                    <span id="disk-usage">{{printf "%.1f%%" .systemInfo.DiskUsage}}</span>
                </div>
                <div class="progress-bar">
                    <div id="disk-progress" class="progress-fill {{if ge .systemInfo.DiskUsage 90}}danger{{else if ge .systemInfo.DiskUsage 70}}warning{{end}}" 
                         style="width: {{.systemInfo.DiskUsage}}%"></div>
                </div>
                <div style="font-size: 0.9rem; color: #666; margin-top: 0.25rem;">
                    <span id="disk-details">{{printf "%.0f MB / %.0f MB" .systemInfo.DiskUsed .systemInfo.DiskTotal}}</span>
                </div>
            </div>
        </div>
    </div>
    {{else}}
    <div class="error">
        无法获取系统信息
    </div>
    {{end}}
</div>

<div class="card">
    <h2>🌐 网络统计</h2>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
        <div class="stat-card">
            <div class="stat-value" id="network-rx-stat">{{if .systemInfo}}{{printf "%.0f MB" .systemInfo.NetworkRx}}{{else}}--{{end}}</div>
            <div class="stat-label">总接收流量</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="network-tx-stat">{{if .systemInfo}}{{printf "%.0f MB" .systemInfo.NetworkTx}}{{else}}--{{end}}</div>
            <div class="stat-label">总发送流量</div>
        </div>
    </div>
</div>

<div class="card">
    <h2>📊 实时监控图表</h2>
    <div style="text-align: center; padding: 2rem; color: #666;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">📈</div>
        <p>实时监控图表功能正在开发中...</p>
        <p style="font-size: 0.9rem; margin-top: 0.5rem;">将支持CPU、内存、网络流量的实时图表显示</p>
    </div>
</div>

<div class="timestamp">
    最后更新: <span id="last-update">{{.timestamp}}</span>
</div>

<script>
function refreshSystemData() {
    fetch('/data/system')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                updateSystemDisplay(data.data);
                document.getElementById('last-update').textContent = new Date().toLocaleString('zh-CN');
            }
        })
        .catch(error => {
            console.error('Failed to refresh system data:', error);
        });
}

function updateSystemDisplay(systemInfo) {
    // 更新CPU使用率
    const cpuUsage = document.getElementById('cpu-usage');
    const cpuProgress = document.getElementById('cpu-progress');
    if (cpuUsage && cpuProgress) {
        cpuUsage.textContent = systemInfo.cpu_usage.toFixed(1) + '%';
        cpuProgress.style.width = systemInfo.cpu_usage + '%';
        cpuProgress.className = 'progress-fill ' + getProgressClass(systemInfo.cpu_usage);
    }
    
    // 更新内存使用率
    const memoryUsage = document.getElementById('memory-usage');
    const memoryProgress = document.getElementById('memory-progress');
    const memoryDetails = document.getElementById('memory-details');
    if (memoryUsage && memoryProgress) {
        memoryUsage.textContent = systemInfo.memory_usage.toFixed(1) + '%';
        memoryProgress.style.width = systemInfo.memory_usage + '%';
        memoryProgress.className = 'progress-fill ' + getProgressClass(systemInfo.memory_usage);
        
        if (memoryDetails) {
            const usedGB = (systemInfo.memory_used / 1073741824).toFixed(2);
            const totalGB = (systemInfo.memory_total / 1073741824).toFixed(2);
            memoryDetails.textContent = usedGB + ' GB / ' + totalGB + ' GB';
        }
    }
    
    // 更新磁盘使用率
    const diskUsage = document.getElementById('disk-usage');
    const diskProgress = document.getElementById('disk-progress');
    const diskDetails = document.getElementById('disk-details');
    if (diskUsage && diskProgress) {
        diskUsage.textContent = systemInfo.disk_usage.toFixed(1) + '%';
        diskProgress.style.width = systemInfo.disk_usage + '%';
        diskProgress.className = 'progress-fill ' + getProgressClass(systemInfo.disk_usage);
        
        if (diskDetails) {
            const usedGB = (systemInfo.disk_used / 1073741824).toFixed(2);
            const totalGB = (systemInfo.disk_total / 1073741824).toFixed(2);
            diskDetails.textContent = usedGB + ' GB / ' + totalGB + ' GB';
        }
    }
    
    // 更新网络统计
    const networkRx = document.getElementById('network-rx-stat');
    const networkTx = document.getElementById('network-tx-stat');
    if (networkRx) {
        networkRx.textContent = (systemInfo.network_rx / 1048576).toFixed(2) + ' MB';
    }
    if (networkTx) {
        networkTx.textContent = (systemInfo.network_tx / 1048576).toFixed(2) + ' MB';
    }
}

function getProgressClass(percentage) {
    if (percentage >= 90) return 'danger';
    if (percentage >= 70) return 'warning';
    return '';
}

// 每5秒自动刷新数据
setInterval(refreshSystemData, 5000);
</script>
{{end}}

{{template "base.html" .}}
