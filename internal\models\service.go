package models

import (
	"time"

	"gorm.io/gorm"
)

// Service represents the services table
type Service struct {
	gorm.Model
	ServerID    uint            `gorm:"not null"`
	Server      Server          `gorm:"foreignKey:ServerID"` // Belongs To Server
	Name        string          `gorm:"not null"`
	Type        string          `gorm:"size:20;not null"` // supervisord/systemd/docker
	DisplayName string          `gorm:"size:200"`
	Description string          `gorm:"type:text"`
	AutoStart   bool            `gorm:"default:false"`
	IsMonitored bool            `gorm:"default:true"`
	Statuses    []ServiceStatus `gorm:"foreignKey:ServiceID"` // Has Many ServiceStatus
}

// ServiceStatus represents the service_status table
type ServiceStatus struct {
	gorm.Model
	ServiceID    uint   `gorm:"not null"`
	Status       string `gorm:"size:20;not null"` // running/stopped/failed
	PID          int
	Uptime       string `gorm:"size:50"`
	MemoryUsage  int
	CPUUsage     float64
	RestartCount int       `gorm:"default:0"`
	ErrorMessage string    `gorm:"type:text"`
	Timestamp    time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}
