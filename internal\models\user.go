package models

import (
	"time"

	"gorm.io/gorm"
)

// User 表示用户表（基本结构，可扩展） (User represents the user table (basic structure, extensible))
type User struct {
	gorm.Model
	Username  string `gorm:"unique;not null"`
	Password  string `gorm:"not null"` // 哈希密码 (Hashed password)
	Email     string `gorm:"unique"`
	Role      string `gorm:"default:'user'"` // 用户角色：admin, user (User role: admin, user)
	LastLogin time.Time
	IsActive  bool `gorm:"default:true"`
}
