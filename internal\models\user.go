package models

import (
	"time"

	"gorm.io/gorm"
)

// User represents the users table (basic structure, can be expanded)
type User struct {
	gorm.Model
	Username  string `gorm:"unique;not null"`
	Password  string `gorm:"not null"` // Hashed password
	Email     string `gorm:"unique"`
	Role      string `gorm:"default:'user'"` // admin, user
	LastLogin time.Time
	IsActive  bool `gorm:"default:true"`
}
