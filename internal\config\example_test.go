package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"
)

// ExampleManager 演示配置管理器的使用
func ExampleManager() {
	// 创建临时配置文件
	tempDir, err := os.MkdirTemp("", "config_example_*")
	if err != nil {
		log.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	configFile := filepath.Join(tempDir, "config.yaml")
	configContent := `
system:
  name: "Example Monitor"
  version: "1.0.0"
  environment: "development"
  mode: "both"

database:
  path: "./data/example.db"
  max_open_conns: 10

scheduler:
  enabled: true
  mode: "odd"
  timezone: "Asia/Shanghai"

servers:
  - name: "example-server"
    ip: "*************"
    port: 5201
    location: "Example Location"
    enabled: true

web:
  enabled: true
  port: 8080

api:
  enabled: true
  port: 8443

log:
  level: "info"
  format: "text"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
`

	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		log.Fatal(err)
	}

	// 创建配置管理器
	manager := NewManager(configFile)

	// 加载配置
	if err := manager.Load(); err != nil {
		log.Fatal(err)
	}

	config := manager.Get()

	// 输出配置信息
	fmt.Printf("System Name: %s\n", config.System.Name)
	fmt.Printf("Environment: %s\n", config.System.Environment)
	fmt.Printf("Database Path: %s\n", config.Database.Path)
	fmt.Printf("Web Port: %d\n", config.Web.Port)
	fmt.Printf("API Port: %d\n", config.API.Port)
	fmt.Printf("Scheduler Mode: %s\n", config.Scheduler.Mode)
	fmt.Printf("Server Count: %d\n", len(config.Servers))

	if len(config.Servers) > 0 {
		fmt.Printf("First Server: %s (%s:%d)\n", 
			config.Servers[0].Name, 
			config.Servers[0].IP, 
			config.Servers[0].Port)
	}

	// Output:
	// System Name: Example Monitor
	// Environment: development
	// Database Path: ./data/example.db
	// Web Port: 8080
	// API Port: 8443
	// Scheduler Mode: odd
	// Server Count: 1
	// First Server: example-server (*************:5201)
}

// ExampleConfig_validation 演示配置验证
func ExampleConfig_validation() {
	// 创建一个无效的配置
	config := &Config{
		System: SystemConfig{
			Name:        "", // 无效：空名称
			Version:     "1.0.0",
			Environment: "invalid_env", // 无效：不支持的环境
			Mode:        "both",
		},
		Servers: []ServerConfig{
			{
				Name: "test-server",
				IP:   "invalid-ip", // 无效：IP格式错误
				Port: 70000,        // 无效：端口超出范围
			},
		},
		Log: LogConfig{
			Level:      "invalid_level", // 无效：不支持的日志级别
			Format:     "text",
			Output:     "stdout",
			MaxSize:    100,
			MaxBackups: 3,
			MaxAge:     28,
		},
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			fmt.Printf("Found %d validation errors:\n", len(ve))
			for _, e := range ve {
				fmt.Printf("- %s: %s\n", e.Field, e.Message)
			}
		} else {
			fmt.Printf("Validation error: %v\n", err)
		}
	}

	// Output:
	// Found 5 validation errors:
	// - system.name: name is required
	// - system.environment: environment must be one of: development, testing, staging, production
	// - servers[0].ip: invalid IP address
	// - servers[0].port: port must be between 1 and 65535
	// - log.level: level must be one of: debug, info, warn, error, fatal, panic
}

// ExampleConfigHotReload 演示配置热重载
func ExampleConfigHotReload() {
	// 创建临时配置文件
	tempDir, err := os.MkdirTemp("", "config_hotreload_*")
	if err != nil {
		log.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	configFile := filepath.Join(tempDir, "config.yaml")
	initialConfig := `
system:
  name: "Initial Config"
  environment: "development"
  mode: "both"

servers:
  - name: "server-01"
    ip: "*************"
    port: 5201
    enabled: true

log:
  level: "info"
  format: "text"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
`

	if err := os.WriteFile(configFile, []byte(initialConfig), 0644); err != nil {
		log.Fatal(err)
	}

	// 创建配置管理器
	manager := NewManager(configFile)

	// 加载配置
	if err := manager.Load(); err != nil {
		log.Fatal(err)
	}

	fmt.Printf("Initial config name: %s\n", manager.Get().System.Name)

	// 注册配置变更回调
	manager.OnConfigChange(func(config *Config) {
		fmt.Printf("Config changed! New name: %s\n", config.System.Name)
	})

	// 启动配置监听（在实际应用中，这会在后台运行）
	// manager.WatchConfig()

	// 模拟配置文件变更
	updatedConfig := `
system:
  name: "Updated Config"
  environment: "development"
  mode: "both"

servers:
  - name: "server-01"
    ip: "*************"
    port: 5201
    enabled: true

log:
  level: "debug"
  format: "text"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
`

	if err := os.WriteFile(configFile, []byte(updatedConfig), 0644); err != nil {
		log.Fatal(err)
	}

	// 手动重新加载配置
	if err := manager.Reload(); err != nil {
		log.Fatal(err)
	}

	fmt.Printf("Reloaded config name: %s\n", manager.Get().System.Name)
	fmt.Printf("Reloaded log level: %s\n", manager.Get().Log.Level)

	// Output:
	// Initial config name: Initial Config
	// Reloaded config name: Updated Config
	// Reloaded log level: debug
}

// ExampleEnvironmentVariables 演示环境变量覆盖
func ExampleEnvironmentVariables() {
	// 设置环境变量
	os.Setenv("MONITOR_SYSTEM_NAME", "Env Override Monitor")
	os.Setenv("MONITOR_WEB_PORT", "9090")
	os.Setenv("MONITOR_LOG_LEVEL", "debug")
	defer func() {
		os.Unsetenv("MONITOR_SYSTEM_NAME")
		os.Unsetenv("MONITOR_WEB_PORT")
		os.Unsetenv("MONITOR_LOG_LEVEL")
	}()

	// 创建配置管理器（不指定配置文件，使用默认值）
	manager := NewManager("")

	// 加载配置
	if err := manager.Load(); err != nil {
		log.Fatal(err)
	}

	config := manager.Get()

	fmt.Printf("System Name (from env): %s\n", config.System.Name)
	fmt.Printf("Web Port (from env): %d\n", config.Web.Port)
	fmt.Printf("Log Level (from env): %s\n", config.Log.Level)

	// Output:
	// System Name (from env): Env Override Monitor
	// Web Port (from env): 9090
	// Log Level (from env): debug
}

// ExampleSchedulerModes 演示调度器模式
func ExampleSchedulerModes() {
	now := time.Now()
	hour := now.Hour()

	fmt.Printf("Current hour: %d\n", hour)

	// 模拟不同的调度模式
	modes := []string{"odd", "even", "both"}

	for _, mode := range modes {
		shouldRun := false
		switch mode {
		case "odd":
			shouldRun = hour%2 == 1
		case "even":
			shouldRun = hour%2 == 0
		case "both":
			shouldRun = true
		}

		fmt.Printf("Mode '%s': should run = %t\n", mode, shouldRun)
	}

	// Output will vary based on current hour
	// Example output:
	// Current hour: 14
	// Mode 'odd': should run = false
	// Mode 'even': should run = true
	// Mode 'both': should run = true
}
