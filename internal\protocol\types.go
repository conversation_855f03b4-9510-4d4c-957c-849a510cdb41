package protocol

import (
	"encoding/json"
	"fmt"
	"time"
)

// MessageType 应用层消息类型
type MessageType string

const (
	// 认证相关
	MessageTypeAuth         MessageType = "auth"
	MessageTypeAuthResponse MessageType = "auth_response"

	// 心跳
	MessageTypeHeartbeat         MessageType = "heartbeat"
	MessageTypeHeartbeatResponse MessageType = "heartbeat_response"

	// 系统信息
	MessageTypeSystemInfo         MessageType = "system_info"
	MessageTypeSystemInfoResponse MessageType = "system_info_response"

	// 测试相关
	MessageTypeTestRequest  MessageType = "test_request"
	MessageTypeTestResponse MessageType = "test_response"
	MessageTypeTestResult   MessageType = "test_result"

	// 配置相关
	MessageTypeConfigUpdate         MessageType = "config_update"
	MessageTypeConfigUpdateResponse MessageType = "config_update_response"

	// 数据同步
	MessageTypeDataSync         MessageType = "data_sync"
	MessageTypeDataSyncResponse MessageType = "data_sync_response"

	// 错误消息
	MessageTypeError MessageType = "error"
)

// ApplicationMessage 应用层消息结构
type ApplicationMessage struct {
	ID        string      `json:"id"`         // 消息ID
	Type      MessageType `json:"type"`       // 消息类型
	Timestamp int64       `json:"timestamp"`  // 时间戳
	From      string      `json:"from"`       // 发送方
	To        string      `json:"to"`         // 接收方
	Data      interface{} `json:"data"`       // 消息数据
}

// AuthData 认证数据
type AuthData struct {
	Username  string `json:"username"`
	Password  string `json:"password"`
	ClientID  string `json:"client_id"`
	Version   string `json:"version"`
	Timestamp int64  `json:"timestamp"`
}

// AuthResponse 认证响应
type AuthResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	Token     string `json:"token,omitempty"`
	ExpiresAt int64  `json:"expires_at,omitempty"`
}

// HeartbeatData 心跳数据
type HeartbeatData struct {
	ClientID  string `json:"client_id"`
	Timestamp int64  `json:"timestamp"`
	Status    string `json:"status"`
}

// SystemInfoData 系统信息数据
type SystemInfoData struct {
	Hostname     string  `json:"hostname"`
	OS           string  `json:"os"`
	Architecture string  `json:"architecture"`
	CPUUsage     float64 `json:"cpu_usage"`
	MemoryUsage  float64 `json:"memory_usage"`
	DiskUsage    float64 `json:"disk_usage"`
	NetworkRx    uint64  `json:"network_rx"`
	NetworkTx    uint64  `json:"network_tx"`
	Uptime       int64   `json:"uptime"`
	LoadAverage  float64 `json:"load_average"`
}

// TestRequestData 测试请求数据
type TestRequestData struct {
	TestID       string `json:"test_id"`
	ServerName   string `json:"server_name"`
	ServerIP     string `json:"server_ip"`
	ServerPort   int    `json:"server_port"`
	Duration     int    `json:"duration"`     // 秒
	Parallel     int    `json:"parallel"`     // 并行连接数
	Protocol     string `json:"protocol"`     // tcp/udp
	Reverse      bool   `json:"reverse"`      // 反向测试
	Bidir        bool   `json:"bidir"`        // 双向测试
	WindowSize   string `json:"window_size"`  // TCP窗口大小
	BufferLength string `json:"buffer_length"` // 缓冲区长度
	Bandwidth    string `json:"bandwidth"`    // 带宽限制
}

// TestResponseData 测试响应数据
type TestResponseData struct {
	TestID  string `json:"test_id"`
	Success bool   `json:"success"`
	Message string `json:"message"`
	StartAt int64  `json:"start_at,omitempty"`
}

// TestResultData 测试结果数据
type TestResultData struct {
	TestID           string  `json:"test_id"`
	ServerName       string  `json:"server_name"`
	ServerIP         string  `json:"server_ip"`
	ServerPort       int     `json:"server_port"`
	Protocol         string  `json:"protocol"`
	Duration         float64 `json:"duration"`
	BytesSent        int64   `json:"bytes_sent"`
	BytesReceived    int64   `json:"bytes_received"`
	BitsPerSecond    float64 `json:"bits_per_second"`
	Jitter           float64 `json:"jitter,omitempty"`
	LostPackets      int     `json:"lost_packets,omitempty"`
	TotalPackets     int     `json:"total_packets,omitempty"`
	LostPercent      float64 `json:"lost_percent,omitempty"`
	Retransmits      int     `json:"retransmits,omitempty"`
	CongestionWindow int     `json:"congestion_window,omitempty"`
	StartTime        int64   `json:"start_time"`
	EndTime          int64   `json:"end_time"`
	Success          bool    `json:"success"`
	ErrorMessage     string  `json:"error_message,omitempty"`
}

// ErrorData 错误数据
type ErrorData struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// NewApplicationMessage 创建新的应用层消息
func NewApplicationMessage(msgType MessageType, from, to string, data interface{}) *ApplicationMessage {
	return &ApplicationMessage{
		ID:        generateMessageID(),
		Type:      msgType,
		Timestamp: time.Now().Unix(),
		From:      from,
		To:        to,
		Data:      data,
	}
}

// ToJSON 将消息转换为JSON
func (m *ApplicationMessage) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// FromJSON 从JSON创建消息
func FromJSON(data []byte) (*ApplicationMessage, error) {
	var msg ApplicationMessage
	if err := json.Unmarshal(data, &msg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal message: %w", err)
	}
	return &msg, nil
}

// Validate 验证消息
func (m *ApplicationMessage) Validate() error {
	if m.ID == "" {
		return fmt.Errorf("message ID is required")
	}
	if m.Type == "" {
		return fmt.Errorf("message type is required")
	}
	if m.From == "" {
		return fmt.Errorf("message from is required")
	}
	if m.Timestamp <= 0 {
		return fmt.Errorf("message timestamp is required")
	}
	return nil
}

// IsExpired 检查消息是否过期
func (m *ApplicationMessage) IsExpired(ttl time.Duration) bool {
	msgTime := time.Unix(m.Timestamp, 0)
	return time.Since(msgTime) > ttl
}

// GetDataAs 将消息数据转换为指定类型
func (m *ApplicationMessage) GetDataAs(target interface{}) error {
	dataBytes, err := json.Marshal(m.Data)
	if err != nil {
		return fmt.Errorf("failed to marshal message data: %w", err)
	}
	
	if err := json.Unmarshal(dataBytes, target); err != nil {
		return fmt.Errorf("failed to unmarshal message data: %w", err)
	}
	
	return nil
}

// CreateAuthMessage 创建认证消息
func CreateAuthMessage(from, to, username, password, clientID, version string) *ApplicationMessage {
	authData := &AuthData{
		Username:  username,
		Password:  password,
		ClientID:  clientID,
		Version:   version,
		Timestamp: time.Now().Unix(),
	}
	return NewApplicationMessage(MessageTypeAuth, from, to, authData)
}

// CreateAuthResponse 创建认证响应
func CreateAuthResponse(from, to string, success bool, message, token string, expiresAt int64) *ApplicationMessage {
	authResp := &AuthResponse{
		Success:   success,
		Message:   message,
		Token:     token,
		ExpiresAt: expiresAt,
	}
	return NewApplicationMessage(MessageTypeAuthResponse, from, to, authResp)
}

// CreateHeartbeat 创建心跳消息
func CreateHeartbeat(from, to, clientID, status string) *ApplicationMessage {
	heartbeat := &HeartbeatData{
		ClientID:  clientID,
		Timestamp: time.Now().Unix(),
		Status:    status,
	}
	return NewApplicationMessage(MessageTypeHeartbeat, from, to, heartbeat)
}

// CreateTestRequest 创建测试请求
func CreateTestRequest(from, to string, testReq *TestRequestData) *ApplicationMessage {
	return NewApplicationMessage(MessageTypeTestRequest, from, to, testReq)
}

// CreateTestResult 创建测试结果
func CreateTestResult(from, to string, testResult *TestResultData) *ApplicationMessage {
	return NewApplicationMessage(MessageTypeTestResult, from, to, testResult)
}

// CreateErrorMessage 创建错误消息
func CreateErrorMessage(from, to string, code int, message, details string) *ApplicationMessage {
	errorData := &ErrorData{
		Code:    code,
		Message: message,
		Details: details,
	}
	return NewApplicationMessage(MessageTypeError, from, to, errorData)
}

// generateMessageID 生成消息ID
func generateMessageID() string {
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Unix())
}
