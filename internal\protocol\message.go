package protocol

import (
	"encoding/binary"
	"fmt"
)

const (
	NonceSize  = 12 // GCM recommended Nonce size
	TagSize    = 16 // GCM authentication tag size
	LengthSize = 4  // Size of the length prefix
)

// Message represents the encrypted data message format
// [4字节长度][12字节Nonce][加密数据][16字节认证标签]
type Message struct {
	Length     uint32 // Length of the encrypted data (ciphertext + tag)
	Nonce      []byte // Nonce used for AES-GCM
	Ciphertext []byte // Encrypted data (including GCM tag)
}

// Marshal serializes the Message into a byte slice
func (m *Message) Marshal() ([]byte, error) {
	if len(m.Nonce) != NonceSize {
		return nil, fmt.Errorf("nonce size must be %d bytes, got %d", NonceSize, len(m.Nonce))
	}

	// Calculate the total length of ciphertext and tag
	// The GCM Seal method already appends the tag to the ciphertext
	m.Length = uint32(len(m.Ciphertext))

	buf := make([]byte, LengthSize+NonceSize+len(m.Ciphertext))

	// Write length
	binary.BigEndian.PutUint32(buf[0:LengthSize], m.Length)
	// Write Nonce
	copy(buf[LengthSize:LengthSize+NonceSize], m.Nonce)
	// Write Ciphertext (which includes the tag)
	copy(buf[LengthSize+NonceSize:], m.Ciphertext)

	return buf, nil
}

// Unmarshal deserializes a byte slice into a Message
func (m *Message) Unmarshal(data []byte) error {
	if len(data) < LengthSize+NonceSize {
		return fmt.Errorf("message data too short to unmarshal")
	}

	// Read length
	m.Length = binary.BigEndian.Uint32(data[0:LengthSize])

	// Read Nonce
	m.Nonce = data[LengthSize : LengthSize+NonceSize]

	// Read Ciphertext (which includes the tag)
	m.Ciphertext = data[LengthSize+NonceSize:]

	if uint32(len(m.Ciphertext)) != m.Length {
		return fmt.Errorf("ciphertext length mismatch: expected %d, got %d", m.Length, len(m.Ciphertext))
	}

	return nil
}
