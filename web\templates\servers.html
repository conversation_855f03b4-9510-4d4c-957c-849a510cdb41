{{define "content"}}
<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <h2>🖥️ 服务器列表</h2>
        <button class="refresh-btn" onclick="refreshServerData()">刷新数据</button>
    </div>
    
    {{if .servers}}
    <div style="overflow-x: auto;">
        <table id="servers-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>IP地址</th>
                    <th>端口</th>
                    <th>位置</th>
                    <th>提供商</th>
                    <th>优先级</th>
                    <th>状态</th>
                    <th>标签</th>
                    <th>创建时间</th>
                </tr>
            </thead>
            <tbody>
                {{range .servers}}
                <tr>
                    <td>{{.ID}}</td>
                    <td><strong>{{.Name}}</strong></td>
                    <td><code>{{.IP}}</code></td>
                    <td>{{.Port}}</td>
                    <td>{{.Location}}</td>
                    <td>{{.Provider}}</td>
                    <td>
                        <span style="background: {{if eq .Priority 1}}#4CAF50{{else if eq .Priority 2}}#FF9800{{else}}#9E9E9E{{end}}; 
                                     color: white; padding: 0.2rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">
                            P{{.Priority}}
                        </span>
                    </td>
                    <td>
                        {{if .IsActive}}
                        <span class="status-active">● 活跃</span>
                        {{else}}
                        <span class="status-inactive">● 停用</span>
                        {{end}}
                    </td>
                    <td>
                        {{if .Tags}}
                        <div style="display: flex; flex-wrap: wrap; gap: 0.25rem;">
                            {{range .Tags}}
                            <span style="background: #e3f2fd; color: #1976d2; padding: 0.1rem 0.3rem; border-radius: 3px; font-size: 0.7rem;">{{.}}</span>
                            {{end}}
                        </div>
                        {{else}}
                        <span style="color: #999;">无</span>
                        {{end}}
                    </td>
                    <td style="font-size: 0.9rem; color: #666;">
                        {{.CreatedAt.Format "2006-01-02 15:04"}}
                    </td>
                </tr>
                {{end}}
            </tbody>
        </table>
    </div>
    
    <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div>
                <strong>总服务器数:</strong> {{len .servers}}
            </div>
            <div>
                <strong>活跃服务器:</strong>
                <span class="status-active" id="active-count">--</span>
            </div>
            <div>
                <strong>停用服务器:</strong>
                <span class="status-inactive" id="inactive-count">--</span>
            </div>
        </div>
    </div>
    {{else}}
    <div style="text-align: center; padding: 2rem; color: #666;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">📭</div>
        <p>暂无服务器数据</p>
        <p style="font-size: 0.9rem; margin-top: 0.5rem;">请检查数据库连接或添加服务器</p>
    </div>
    {{end}}
</div>

<div class="card">
    <h2>📍 服务器分布</h2>
    {{if .servers}}
    <div id="location-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
        <!-- 将通过JavaScript动态生成 -->
    </div>
    {{else}}
    <div style="text-align: center; padding: 1rem; color: #666;">
        暂无分布数据
    </div>
    {{end}}
</div>

<div class="card">
    <h2>🏷️ 服务器管理</h2>
    <div style="text-align: center; padding: 2rem; color: #666;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">🚧</div>
        <p>服务器管理功能正在开发中...</p>
        <p style="font-size: 0.9rem; margin-top: 0.5rem;">将支持添加、编辑、删除服务器等功能</p>
        <div style="margin-top: 1rem;">
            <button class="refresh-btn" style="margin: 0 0.5rem;" disabled>添加服务器</button>
            <button class="refresh-btn" style="margin: 0 0.5rem;" disabled>批量导入</button>
            <button class="refresh-btn" style="margin: 0 0.5rem;" disabled>导出配置</button>
        </div>
    </div>
</div>

<div class="timestamp">
    最后更新: <span id="last-update">{{.timestamp}}</span>
</div>

<script>
function refreshServerData() {
    fetch('/data/servers')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                updateServerTable(data.data);
                document.getElementById('last-update').textContent = new Date().toLocaleString('zh-CN');
            }
        })
        .catch(error => {
            console.error('Failed to refresh server data:', error);
        });
}

function updateServerTable(servers) {
    const tbody = document.querySelector('#servers-table tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    servers.forEach(server => {
        const row = document.createElement('tr');
        
        // 格式化标签
        let tagsHtml = '<span style="color: #999;">无</span>';
        if (server.tags && server.tags.length > 0) {
            tagsHtml = '<div style="display: flex; flex-wrap: wrap; gap: 0.25rem;">';
            server.tags.forEach(tag => {
                tagsHtml += `<span style="background: #e3f2fd; color: #1976d2; padding: 0.1rem 0.3rem; border-radius: 3px; font-size: 0.7rem;">${tag}</span>`;
            });
            tagsHtml += '</div>';
        }
        
        // 优先级颜色
        let priorityColor = '#9E9E9E';
        if (server.priority === 1) priorityColor = '#4CAF50';
        else if (server.priority === 2) priorityColor = '#FF9800';
        
        row.innerHTML = `
            <td>${server.id}</td>
            <td><strong>${server.name}</strong></td>
            <td><code>${server.ip}</code></td>
            <td>${server.port}</td>
            <td>${server.location}</td>
            <td>${server.provider}</td>
            <td>
                <span style="background: ${priorityColor}; color: white; padding: 0.2rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">
                    P${server.priority}
                </span>
            </td>
            <td>
                ${server.is_active ? 
                    '<span class="status-active">● 活跃</span>' : 
                    '<span class="status-inactive">● 停用</span>'
                }
            </td>
            <td>${tagsHtml}</td>
            <td style="font-size: 0.9rem; color: #666;">
                ${new Date(server.created_at).toLocaleString('zh-CN')}
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// 计算服务器统计
function updateServerStats() {
    const rows = document.querySelectorAll('#servers-table tbody tr');
    let activeCount = 0;
    let inactiveCount = 0;
    const locationStats = {};

    rows.forEach(row => {
        const statusCell = row.cells[7]; // 状态列
        const locationCell = row.cells[4]; // 位置列

        if (statusCell && statusCell.textContent.includes('活跃')) {
            activeCount++;
        } else if (statusCell && statusCell.textContent.includes('停用')) {
            inactiveCount++;
        }

        // 统计位置分布
        if (locationCell) {
            const location = locationCell.textContent.trim();
            locationStats[location] = (locationStats[location] || 0) + 1;
        }
    });

    const activeElement = document.getElementById('active-count');
    const inactiveElement = document.getElementById('inactive-count');

    if (activeElement) activeElement.textContent = activeCount;
    if (inactiveElement) inactiveElement.textContent = inactiveCount;

    // 更新位置统计
    updateLocationStats(locationStats);
}

// 更新位置统计显示
function updateLocationStats(locationStats) {
    const container = document.getElementById('location-stats');
    if (!container) return;

    container.innerHTML = '';

    Object.entries(locationStats).forEach(([location, count]) => {
        const statCard = document.createElement('div');
        statCard.className = 'stat-card';
        statCard.innerHTML = `
            <div class="stat-value">${count}</div>
            <div class="stat-label">${location}</div>
        `;
        container.appendChild(statCard);
    });
}

// 页面加载时计算统计
document.addEventListener('DOMContentLoaded', updateServerStats);

// 每30秒自动刷新数据
setInterval(refreshServerData, 30000);
</script>
{{end}}

{{template "base.html" .}}
