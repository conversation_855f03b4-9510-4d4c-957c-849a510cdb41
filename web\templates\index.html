{{define "content"}}
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value">{{.totalServers}}</div>
        <div class="stat-label">总服务器数</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{.activeServers}}</div>
        <div class="stat-label">活跃服务器</div>
    </div>
    {{if .systemInfo}}
    <div class="stat-card">
        <div class="stat-value">{{printf "%.1f%%" .systemInfo.CPUUsage}}</div>
        <div class="stat-label">CPU使用率</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{printf "%.1f%%" .systemInfo.MemoryUsage}}</div>
        <div class="stat-label">内存使用率</div>
    </div>
    {{end}}
</div>

<div class="card">
    <h2>📊 系统概览</h2>
    {{if .systemInfo}}
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
        <div>
            <h3>🖥️ 系统信息</h3>
            <p><strong>主机名:</strong> {{.systemInfo.Hostname}}</p>
            <p><strong>操作系统:</strong> {{.systemInfo.OS}}</p>
            <p><strong>架构:</strong> {{.systemInfo.Architecture}}</p>
            <p><strong>运行时间:</strong> {{.systemInfo.Uptime}} 秒</p>
        </div>
        
        <div>
            <h3>💾 资源使用</h3>
            <div style="margin: 0.5rem 0;">
                <span>CPU使用率: {{printf "%.1f%%" .systemInfo.CPUUsage}}</span>
                <div class="progress-bar">
                    <div class="progress-fill {{if ge .systemInfo.CPUUsage 90}}danger{{else if ge .systemInfo.CPUUsage 70}}warning{{end}}" 
                         style="width: {{.systemInfo.CPUUsage}}%"></div>
                </div>
            </div>
            
            <div style="margin: 0.5rem 0;">
                <span>内存使用率: {{printf "%.1f%%" .systemInfo.MemoryUsage}}</span>
                <div class="progress-bar">
                    <div class="progress-fill {{if ge .systemInfo.MemoryUsage 90}}danger{{else if ge .systemInfo.MemoryUsage 70}}warning{{end}}" 
                         style="width: {{.systemInfo.MemoryUsage}}%"></div>
                </div>
            </div>
            
            <div style="margin: 0.5rem 0;">
                <span>磁盘使用率: {{printf "%.1f%%" .systemInfo.DiskUsage}}</span>
                <div class="progress-bar">
                    <div class="progress-fill {{if ge .systemInfo.DiskUsage 90}}danger{{else if ge .systemInfo.DiskUsage 70}}warning{{end}}" 
                         style="width: {{.systemInfo.DiskUsage}}%"></div>
                </div>
            </div>
        </div>
        
        <div>
            <h3>🌐 网络流量</h3>
            <p><strong>接收:</strong> <span id="network-rx">{{printf "%.2f MB" .systemInfo.NetworkRx}}</span></p>
            <p><strong>发送:</strong> <span id="network-tx">{{printf "%.2f MB" .systemInfo.NetworkTx}}</span></p>
            {{if gt .systemInfo.LoadAverage 0}}
            <p><strong>负载平均值:</strong> {{printf "%.2f" .systemInfo.LoadAverage}}</p>
            {{end}}
        </div>
    </div>
    {{else}}
    <div class="error">
        无法获取系统信息
    </div>
    {{end}}
</div>

<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <h2>🚀 快速操作</h2>
        <button class="refresh-btn" onclick="window.location.reload()">刷新数据</button>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
        <a href="/system" style="text-decoration: none;">
            <div style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 1rem; border-radius: 8px; text-align: center; transition: transform 0.3s;" 
                 onmouseover="this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.transform='translateY(0)'">
                <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">📈</div>
                <div>系统监控</div>
            </div>
        </a>
        
        <a href="/servers" style="text-decoration: none;">
            <div style="background: linear-gradient(135deg, #2196F3, #1976D2); color: white; padding: 1rem; border-radius: 8px; text-align: center; transition: transform 0.3s;" 
                 onmouseover="this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.transform='translateY(0)'">
                <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">🖥️</div>
                <div>服务器管理</div>
            </div>
        </a>
        
        <a href="/stats" style="text-decoration: none;">
            <div style="background: linear-gradient(135deg, #FF9800, #F57C00); color: white; padding: 1rem; border-radius: 8px; text-align: center; transition: transform 0.3s;" 
                 onmouseover="this.style.transform='translateY(-2px)'" 
                 onmouseout="this.style.transform='translateY(0)'">
                <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">📊</div>
                <div>统计信息</div>
            </div>
        </a>
        
        <div style="background: linear-gradient(135deg, #9C27B0, #7B1FA2); color: white; padding: 1rem; border-radius: 8px; text-align: center; opacity: 0.6;">
            <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">⚡</div>
            <div>性能测试</div>
            <div style="font-size: 0.8rem; margin-top: 0.5rem;">即将推出</div>
        </div>
    </div>
</div>

<div class="timestamp">
    最后更新: {{.timestamp}}
</div>

<script>
// 实时更新网络流量数据
function updateNetworkStats() {
    fetch('/data/system')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                const rxElement = document.getElementById('network-rx');
                const txElement = document.getElementById('network-tx');
                
                if (rxElement) {
                    rxElement.textContent = (data.data.network_rx / 1048576).toFixed(2) + ' MB';
                }
                if (txElement) {
                    txElement.textContent = (data.data.network_tx / 1048576).toFixed(2) + ' MB';
                }
            }
        })
        .catch(error => console.log('Network stats update failed:', error));
}

// 每10秒更新一次网络统计
setInterval(updateNetworkStats, 10000);
</script>
{{end}}

{{template "base.html" .}}
