package api

import (
	"time"

	"github.com/gin-gonic/gin"
)

// LoggerMiddleware 记录请求详细信息。(LoggerMiddleware logs request details.)
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		// 记录请求详细信息，例如：路径、方法、状态、持续时间 (Logs request details, e.g., path, method, status, duration)
		// 目前，我们只打印到控制台。在实际应用中，请使用适当的日志记录器。(Currently, we only print to the console. In a real application, use an appropriate logger.)
		// fmt.Printf("请求 - 路径：%s，方法：%s，状态：%d，持续时间：%v\n",
		// 	c.Request.URL.Path, c.Request.Method, c.Writer.Status(), duration)
		_ = duration // 避免未使用变量警告
	}
}
