package api

import (
	"time"

	"github.com/gin-gonic/gin"
)

// LoggerMiddleware logs the request details.
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		// Log request details, e.g., path, method, status, duration
		// For now, we'll just print to console. In a real app, use a proper logger.
		// fmt.Printf("Request - Path: %s, Method: %s, Status: %d, Duration: %v\n",
		// 	c.Request.URL.Path, c.Request.Method, c.Writer.Status(), duration)
		_ = duration // Avoid unused variable warning
	}
}
