package models

import (
	"time"

	"gorm.io/gorm"
)

// Alert 表示警报表
type Alert struct {
	gorm.Model
	ServerID  uint      `gorm:"not null"`
	Server    Server    `gorm:"foreignKey:ServerID"` // 属于服务器
	Metric    string    `gorm:"not null"`            // 例如："cpu_usage", "memory_usage"
	Threshold float64   `gorm:"not null"`
	Operator  string    `gorm:"size:10;not null"` // 例如：">", "<", "="
	Value     float64   `gorm:"not null"`
	Message   string    `gorm:"type:text"`
	Level     string    `gorm:"size:20;not null"` // 例如："info", "warning", "critical"
	Status    string    `gorm:"size:20;not null"` // 例如："active", "resolved"
	Timestamp time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}
