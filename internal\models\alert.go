package models

import (
	"time"

	"gorm.io/gorm"
)

// <PERSON><PERSON> represents the alerts table
type <PERSON>ert struct {
	gorm.Model
	ServerID  uint      `gorm:"not null"`
	Server    Server    `gorm:"foreignKey:ServerID"` // Belongs To Server
	Metric    string    `gorm:"not null"`            // e.g., "cpu_usage", "memory_usage"
	Threshold float64   `gorm:"not null"`
	Operator  string    `gorm:"size:10;not null"` // e.g., ">", "<", "="
	Value     float64   `gorm:"not null"`
	Message   string    `gorm:"type:text"`
	Level     string    `gorm:"size:20;not null"` // e.g., "info", "warning", "critical"
	Status    string    `gorm:"size:20;not null"` // e.g., "active", "resolved"
	Timestamp time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}
