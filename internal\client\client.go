package client

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"server-monitor/internal/config"
)

// Client 远程监控客户端
type Client struct {
	config     *config.Config
	httpClient *http.Client
	baseURL    string
}

// NewClient 创建新的客户端
func NewClient(cfg *config.Config) *Client {
	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // 开发环境跳过证书验证
			},
		},
	}

	// 构建基础URL
	baseURL := fmt.Sprintf("http://%s:%d/api/v1", cfg.API.Host, cfg.API.Port)
	if cfg.API.TLS.Enabled {
		baseURL = fmt.Sprintf("https://%s:%d/api/v1", cfg.API.Host, cfg.API.Port)
	}

	return &Client{
		config:     cfg,
		httpClient: httpClient,
		baseURL:    baseURL,
	}
}

// SystemInfo 系统信息响应
type SystemInfo struct {
	Hostname     string  `json:"hostname"`
	OS           string  `json:"os"`
	Platform     string  `json:"platform"`
	Architecture string  `json:"architecture"`
	Uptime       uint64  `json:"uptime"`
	CPUUsage     float64 `json:"cpu_usage"`
	MemoryUsage  float64 `json:"memory_usage"`
	MemoryUsed   uint64  `json:"memory_used"`
	MemoryTotal  uint64  `json:"memory_total"`
	DiskUsage    float64 `json:"disk_usage"`
	DiskUsed     uint64  `json:"disk_used"`
	DiskTotal    uint64  `json:"disk_total"`
	NetworkRx    uint64  `json:"network_rx"`
	NetworkTx    uint64  `json:"network_tx"`
	LoadAverage  float64 `json:"load_average"`
	Timestamp    int64   `json:"timestamp"`
}

// Server 服务器信息
type Server struct {
	ID       int      `json:"id"`
	Name     string   `json:"name"`
	IP       string   `json:"ip"`
	Port     int      `json:"port"`
	Location string   `json:"location"`
	Provider string   `json:"provider"`
	IsActive bool     `json:"is_active"`
	Priority int      `json:"priority"`
	Tags     []string `json:"tags"`
}

// APIResponse 通用API响应
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// GetSystemInfo 获取系统信息
func (c *Client) GetSystemInfo(ctx context.Context) (*SystemInfo, error) {
	url := c.baseURL + "/system/info"

	resp, err := c.doRequest(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	var apiResp APIResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("API错误: %s", apiResp.Error)
	}

	// 将data转换为SystemInfo
	dataBytes, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("数据序列化失败: %w", err)
	}

	var systemInfo SystemInfo
	if err := json.Unmarshal(dataBytes, &systemInfo); err != nil {
		return nil, fmt.Errorf("系统信息解析失败: %w", err)
	}

	return &systemInfo, nil
}

// ListServers 获取服务器列表
func (c *Client) ListServers(ctx context.Context) ([]*Server, error) {
	url := c.baseURL + "/servers"

	resp, err := c.doRequest(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	var apiResp APIResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("API错误: %s", apiResp.Error)
	}

	// 将data转换为Server数组
	dataBytes, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("数据序列化失败: %w", err)
	}

	var servers []*Server
	if err := json.Unmarshal(dataBytes, &servers); err != nil {
		return nil, fmt.Errorf("服务器列表解析失败: %w", err)
	}

	return servers, nil
}

// CreateServer 创建服务器
func (c *Client) CreateServer(ctx context.Context, server *Server) (*Server, error) {
	url := c.baseURL + "/servers"

	reqBody, err := json.Marshal(server)
	if err != nil {
		return nil, fmt.Errorf("请求序列化失败: %w", err)
	}

	resp, err := c.doRequest(ctx, "POST", url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	var apiResp APIResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("API错误: %s", apiResp.Error)
	}

	// 将data转换为Server
	dataBytes, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("数据序列化失败: %w", err)
	}

	var createdServer Server
	if err := json.Unmarshal(dataBytes, &createdServer); err != nil {
		return nil, fmt.Errorf("服务器信息解析失败: %w", err)
	}

	return &createdServer, nil
}

// GetServer 获取指定服务器
func (c *Client) GetServer(ctx context.Context, id int) (*Server, error) {
	url := fmt.Sprintf("%s/servers/%d", c.baseURL, id)

	resp, err := c.doRequest(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	var apiResp APIResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("API错误: %s", apiResp.Error)
	}

	// 将data转换为Server
	dataBytes, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("数据序列化失败: %w", err)
	}

	var server Server
	if err := json.Unmarshal(dataBytes, &server); err != nil {
		return nil, fmt.Errorf("服务器信息解析失败: %w", err)
	}

	return &server, nil
}

// UpdateServer 更新服务器
func (c *Client) UpdateServer(ctx context.Context, server *Server) (*Server, error) {
	url := fmt.Sprintf("%s/servers/%d", c.baseURL, server.ID)

	reqBody, err := json.Marshal(server)
	if err != nil {
		return nil, fmt.Errorf("请求序列化失败: %w", err)
	}

	resp, err := c.doRequest(ctx, "PUT", url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	var apiResp APIResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("API错误: %s", apiResp.Error)
	}

	// 将data转换为Server
	dataBytes, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("数据序列化失败: %w", err)
	}

	var updatedServer Server
	if err := json.Unmarshal(dataBytes, &updatedServer); err != nil {
		return nil, fmt.Errorf("服务器信息解析失败: %w", err)
	}

	return &updatedServer, nil
}

// DeleteServer 删除服务器
func (c *Client) DeleteServer(ctx context.Context, id int) error {
	url := fmt.Sprintf("%s/servers/%d", c.baseURL, id)

	resp, err := c.doRequest(ctx, "DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("请求失败: %w", err)
	}

	var apiResp APIResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if !apiResp.Success {
		return fmt.Errorf("API错误: %s", apiResp.Error)
	}

	return nil
}

// HealthCheck 健康检查
func (c *Client) HealthCheck(ctx context.Context) error {
	url := c.baseURL + "/health"

	resp, err := c.doRequest(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("健康检查失败: %w", err)
	}

	var apiResp APIResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if !apiResp.Success {
		return fmt.Errorf("健康检查失败: %s", apiResp.Error)
	}

	return nil
}

// doRequest 执行HTTP请求
func (c *Client) doRequest(ctx context.Context, method, url string, body []byte) ([]byte, error) {
	var reqBody io.Reader
	if body != nil {
		reqBody = bytes.NewReader(body)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "ServerMonitor-Client/1.0")

	// 执行请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("执行请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP错误: %d %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}
