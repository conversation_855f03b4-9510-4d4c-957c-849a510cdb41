package web

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/monitor"

	"github.com/gin-gonic/gin"
)

// Server Web服务器 (Web Server)
type Server struct {
	config        *config.Config
	repository    database.Repository
	systemMonitor *monitor.SystemMonitor
	httpServer    *http.Server
	router        *gin.Engine
}

// NewServer 创建新的Web服务器 (Create new Web server)
func NewServer(cfg *config.Config, repo database.Repository, sysMon *monitor.SystemMonitor) *Server {
	// 设置Gin模式
	if cfg.System.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	router := gin.New()

	// 添加中间件 (Add middleware)
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	server := &Server{
		config:        cfg,
		repository:    repo,
		systemMonitor: sysMon,
		router:        router,
	}

	// 设置路由 (Set up routes)
	server.setupRoutes()

	return server
}

// setupRoutes 设置路由 (Set up routes)
func (s *Server) setupRoutes() {
	// 静态文件服务 (Static file service)
	if s.config.Web.StaticDir != "" {
		s.router.Static("/static", s.config.Web.StaticDir)
	}

	// 主页 (Home page)
	s.router.GET("/", s.indexPage)

	// 系统监控页面 (System monitoring page)
	s.router.GET("/system", s.systemPage)

	// 服务器管理页面 (Server management page)
	s.router.GET("/servers", s.serversPage)

	// 统计页面 (Statistics page)
	s.router.GET("/stats", s.statsPage)

	// API数据端点（用于AJAX请求） (API data endpoint (for AJAX requests))
	api := s.router.Group("/data")
	{
		api.GET("/system", s.getSystemData)
		api.GET("/servers", s.getServersData)
		api.GET("/stats", s.getStatsData)
	}
}

// Start 启动Web服务器 (Start Web server)
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Web.Host, s.config.Web.Port)

	s.httpServer = &http.Server{
		Addr:         addr,
		Handler:      s.router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  time.Minute,
	}

	fmt.Printf("Web server starting on %s\n", addr)

	if s.config.Web.TLS.Enabled {
		return s.httpServer.ListenAndServeTLS(
			s.config.Web.TLS.CertFile,
			s.config.Web.TLS.KeyFile,
		)
	}

	return s.httpServer.ListenAndServe()
}

// Stop 停止Web服务器 (Stop Web server)
func (s *Server) Stop(ctx context.Context) error {
	if s.httpServer == nil {
		return nil
	}

	fmt.Println("Stopping Web server...")
	return s.httpServer.Shutdown(ctx)
}

// indexPage 主页 (Home page)
func (s *Server) indexPage(c *gin.Context) {
	// 获取基本统计信息 (Get basic statistics)
	servers, _ := s.repository.ListServers(false)
	systemInfo, _ := s.systemMonitor.GetSystemInfo()

	activeServers := 0
	for _, server := range servers {
		if server.IsActive {
			activeServers++
		}
	}

	data := gin.H{
		"title":         "服务器监控系统",
		"totalServers":  len(servers),
		"activeServers": activeServers,
		"systemInfo":    systemInfo,
		"timestamp":     time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "index.html", data)
}

// systemPage 系统监控页面 (System monitoring page)
func (s *Server) systemPage(c *gin.Context) {
	systemInfo, err := s.systemMonitor.GetSystemInfo()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"title": "错误",
			"error": err.Error(),
		})
		return
	}

	data := gin.H{
		"title":      "系统监控",
		"systemInfo": systemInfo,
		"timestamp":  time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "system.html", data)
}

// serversPage 服务器管理页面 (Server management page)
func (s *Server) serversPage(c *gin.Context) {
	servers, err := s.repository.ListServers(false)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"title": "错误",
			"error": err.Error(),
		})
		return
	}

	data := gin.H{
		"title":     "服务器管理",
		"servers":   servers,
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "servers.html", data)
}

// statsPage 统计页面 (Statistics page)
func (s *Server) statsPage(c *gin.Context) {
	servers, _ := s.repository.ListServers(false)
	systemInfo, _ := s.systemMonitor.GetSystemInfo()

	activeServers := 0
	for _, server := range servers {
		if server.IsActive {
			activeServers++
		}
	}

	data := gin.H{
		"title":         "统计信息",
		"totalServers":  len(servers),
		"activeServers": activeServers,
		"systemInfo":    systemInfo,
		"servers":       servers,
		"timestamp":     time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "stats.html", data)
}

// getSystemData 获取系统数据（AJAX） (Get system data (AJAX))
func (s *Server) getSystemData(c *gin.Context) {
	systemInfo, err := s.systemMonitor.GetSystemInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    systemInfo,
	})
}

// getServersData 获取服务器数据（AJAX） (Get server data (AJAX))
func (s *Server) getServersData(c *gin.Context) {
	servers, err := s.repository.ListServers(false)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    servers,
	})
}

// getStatsData 获取统计数据（AJAX） (Get statistics data (AJAX))
func (s *Server) getStatsData(c *gin.Context) {
	servers, _ := s.repository.ListServers(false)
	systemInfo, _ := s.systemMonitor.GetSystemInfo()

	activeServers := 0
	for _, server := range servers {
		if server.IsActive {
			activeServers++
		}
	}

	stats := gin.H{
		"servers": gin.H{
			"total":  len(servers),
			"active": activeServers,
		},
		"system":    systemInfo,
		"timestamp": time.Now().Unix(),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// LoadTemplates 加载HTML模板 (Load HTML templates)
func (s *Server) LoadTemplates() error {
	if s.config.Web.TemplateDir == "" {
		// 使用内嵌模板 (Use embedded templates)
		s.loadEmbeddedTemplates()
		return nil
	}

	// 从文件系统加载模板 (Load templates from file system)
	pattern := filepath.Join(s.config.Web.TemplateDir, "*.html")
	s.router.LoadHTMLGlob(pattern)

	return nil
}

// loadEmbeddedTemplates 加载内嵌模板 (Load embedded templates)
func (s *Server) loadEmbeddedTemplates() {
	// 加载模板文件
	s.router.LoadHTMLGlob("web/templates/*.html")
}
