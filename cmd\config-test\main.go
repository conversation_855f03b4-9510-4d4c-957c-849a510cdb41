package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"server-monitor/internal/config"
)

func main() {
	var (
		configFile = flag.String("config", "./configs/config.yaml", "配置文件路径")
		validate   = flag.Bool("validate", false, "仅验证配置文件")
		showConfig = flag.Bool("show", false, "显示配置内容")
		testEnv    = flag.Bool("test-env", false, "测试环境变量覆盖")
	)
	flag.Parse()

	if *testEnv {
		testEnvironmentVariables()
		return
	}

	// 创建配置管理器
	manager := config.NewManager(*configFile)

	// 加载配置
	fmt.Printf("Loading config from: %s\n", *configFile)
	if err := manager.Load(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	cfg := manager.Get()

	// 验证配置
	if err := cfg.Validate(); err != nil {
		if *validate {
			fmt.Printf("❌ Configuration validation failed:\n")
			if ve, ok := err.(config.ValidationErrors); ok {
				for _, e := range ve {
					fmt.Printf("  - %s: %s\n", e.Field, e.Message)
				}
			} else {
				fmt.Printf("  - %v\n", err)
			}
			os.Exit(1)
		} else {
			log.Printf("Warning: Configuration validation failed: %v", err)
		}
	} else {
		fmt.Printf("✅ Configuration validation passed\n")
	}

	if *validate {
		fmt.Println("Configuration is valid!")
		return
	}

	if *showConfig {
		showConfiguration(cfg)
	}

	// 测试配置功能
	testConfigurationFeatures(manager)
}

func showConfiguration(cfg *config.Config) {
	fmt.Println("\n=== Configuration Details ===")

	// 系统配置
	fmt.Printf("System:\n")
	fmt.Printf("  Name: %s\n", cfg.System.Name)
	fmt.Printf("  Version: %s\n", cfg.System.Version)
	fmt.Printf("  Environment: %s\n", cfg.System.Environment)
	fmt.Printf("  Mode: %s\n", cfg.System.Mode)
	fmt.Printf("  Data Dir: %s\n", cfg.System.DataDir)

	// 数据库配置
	fmt.Printf("\nDatabase:\n")
	fmt.Printf("  Path: %s\n", cfg.Database.Path)
	fmt.Printf("  Max Open Conns: %d\n", cfg.Database.MaxOpenConns)
	fmt.Printf("  Max Idle Conns: %d\n", cfg.Database.MaxIdleConns)
	fmt.Printf("  Enable WAL: %t\n", cfg.Database.EnableWAL)

	// 调度器配置
	fmt.Printf("\nScheduler:\n")
	fmt.Printf("  Enabled: %t\n", cfg.Scheduler.Enabled)
	fmt.Printf("  Mode: %s\n", cfg.Scheduler.Mode)
	fmt.Printf("  Timezone: %s\n", cfg.Scheduler.Timezone)
	fmt.Printf("  Max Concurrent: %d\n", cfg.Scheduler.MaxConcurrent)

	// 服务器配置
	fmt.Printf("\nServers (%d):\n", len(cfg.Servers))
	for i, server := range cfg.Servers {
		fmt.Printf("  [%d] %s (%s:%d) - %s\n",
			i+1, server.Name, server.IP, server.Port, server.Location)
		fmt.Printf("      Provider: %s, Enabled: %t, Priority: %d\n",
			server.Provider, server.Enabled, server.Priority)
	}

	// 对端配置
	fmt.Printf("\nPeer:\n")
	fmt.Printf("  Enabled: %t\n", cfg.Peer.Enabled)
	if cfg.Peer.Enabled {
		fmt.Printf("  IP: %s:%d\n", cfg.Peer.IP, cfg.Peer.Port)
		fmt.Printf("  Username: %s\n", cfg.Peer.Username)
	}

	// 测试配置
	fmt.Printf("\nTest:\n")
	fmt.Printf("  Duration: %s\n", cfg.Test.Duration)
	fmt.Printf("  Parallel: %d\n", cfg.Test.Parallel)
	fmt.Printf("  Protocol: %s\n", cfg.Test.Protocol)
	fmt.Printf("  Window Size: %s\n", cfg.Test.WindowSize)

	// Web配置
	fmt.Printf("\nWeb:\n")
	fmt.Printf("  Enabled: %t\n", cfg.Web.Enabled)
	if cfg.Web.Enabled {
		fmt.Printf("  Address: %s:%d\n", cfg.Web.Host, cfg.Web.Port)
		fmt.Printf("  TLS: %t\n", cfg.Web.TLS.Enabled)
	}

	// API配置
	fmt.Printf("\nAPI:\n")
	fmt.Printf("  Enabled: %t\n", cfg.API.Enabled)
	if cfg.API.Enabled {
		fmt.Printf("  Address: %s:%d\n", cfg.API.Host, cfg.API.Port)
		fmt.Printf("  Prefix: %s\n", cfg.API.Prefix)
		fmt.Printf("  TLS: %t\n", cfg.API.TLS.Enabled)
		fmt.Printf("  CORS: %t\n", cfg.API.CORS.Enabled)
	}

	// 同步配置
	fmt.Printf("\nSync:\n")
	fmt.Printf("  Enabled: %t\n", cfg.Sync.Enabled)
	if cfg.Sync.Enabled {
		fmt.Printf("  Interval: %s\n", cfg.Sync.Interval)
		fmt.Printf("  Batch Size: %d\n", cfg.Sync.BatchSize)
	}

	// 日志配置
	fmt.Printf("\nLog:\n")
	fmt.Printf("  Level: %s\n", cfg.Log.Level)
	fmt.Printf("  Format: %s\n", cfg.Log.Format)
	fmt.Printf("  Output: %s\n", cfg.Log.Output)
	if cfg.Log.Output == "file" {
		fmt.Printf("  File: %s\n", cfg.Log.File)
		fmt.Printf("  Max Size: %d MB\n", cfg.Log.MaxSize)
		fmt.Printf("  Max Backups: %d\n", cfg.Log.MaxBackups)
		fmt.Printf("  Max Age: %d days\n", cfg.Log.MaxAge)
	}
}

func testConfigurationFeatures(manager *config.Manager) {
	fmt.Println("\n=== Testing Configuration Features ===")

	// 测试Viper实例访问
	viper := manager.GetViper()
	fmt.Printf("✅ Viper instance accessible\n")
	fmt.Printf("   Config file used: %s\n", viper.ConfigFileUsed())

	// 测试配置重载
	fmt.Printf("✅ Testing config reload...\n")
	if err := manager.Reload(); err != nil {
		fmt.Printf("❌ Reload failed: %v\n", err)
	} else {
		fmt.Printf("✅ Reload successful\n")
	}

	// 测试配置回调
	fmt.Printf("✅ Testing config change callback...\n")
	manager.OnConfigChange(func(cfg *config.Config) {
		fmt.Printf("   Callback triggered for config: %s\n", cfg.System.Name)
	})
	fmt.Printf("✅ Callback registered\n")

	fmt.Println("\n=== Configuration Test Complete ===")
}

func testEnvironmentVariables() {
	fmt.Println("=== Testing Environment Variable Override ===")

	// 设置测试环境变量
	testVars := map[string]string{
		"MONITOR_SYSTEM_NAME":    "Env Test Monitor",
		"MONITOR_WEB_PORT":       "9090",
		"MONITOR_API_PORT":       "9443",
		"MONITOR_LOG_LEVEL":      "debug",
		"MONITOR_SCHEDULER_MODE": "even",
	}

	fmt.Println("Setting environment variables:")
	for key, value := range testVars {
		os.Setenv(key, value)
		fmt.Printf("  %s = %s\n", key, value)
	}

	// 创建配置管理器（不指定配置文件）
	manager := config.NewManager("")

	// 加载配置
	if err := manager.Load(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	cfg := manager.Get()

	fmt.Println("\nConfiguration with environment overrides:")
	fmt.Printf("  System Name: %s\n", cfg.System.Name)
	fmt.Printf("  Web Port: %d\n", cfg.Web.Port)
	fmt.Printf("  API Port: %d\n", cfg.API.Port)
	fmt.Printf("  Log Level: %s\n", cfg.Log.Level)
	fmt.Printf("  Scheduler Mode: %s\n", cfg.Scheduler.Mode)

	// 清理环境变量
	fmt.Println("\nCleaning up environment variables...")
	for key := range testVars {
		os.Unsetenv(key)
	}

	fmt.Println("✅ Environment variable test complete")
}
