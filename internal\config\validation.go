package config

import (
	"fmt"
	"net"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// ValidationError 验证错误
type ValidationError struct {
	Field   string
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error for field '%s': %s", e.Field, e.Message)
}

// ValidationErrors 多个验证错误
type ValidationErrors []ValidationError

func (e ValidationErrors) Error() string {
	var messages []string
	for _, err := range e {
		messages = append(messages, err.Error())
	}
	return strings.Join(messages, "; ")
}

// Validate 验证配置
func (c *Config) Validate() error {
	var errors ValidationErrors

	// 验证系统配置
	if err := c.validateSystem(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "system", Message: err.Error()})
		}
	}

	// 验证数据库配置
	if err := c.validateDatabase(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "database", Message: err.Error()})
		}
	}

	// 验证调度器配置
	if err := c.validateScheduler(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "scheduler", Message: err.Error()})
		}
	}

	// 验证服务器配置
	if err := c.validateServers(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "servers", Message: err.Error()})
		}
	}

	// 验证对端配置
	if err := c.validatePeer(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "peer", Message: err.Error()})
		}
	}

	// 验证测试配置
	if err := c.validateTest(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "test", Message: err.Error()})
		}
	}

	// 验证Web配置
	if err := c.validateWeb(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "web", Message: err.Error()})
		}
	}

	// 验证API配置
	if err := c.validateAPI(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "api", Message: err.Error()})
		}
	}

	// 验证同步配置
	if err := c.validateSync(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "sync", Message: err.Error()})
		}
	}

	// 验证日志配置
	if err := c.validateLog(); err != nil {
		if ve, ok := err.(ValidationErrors); ok {
			errors = append(errors, ve...)
		} else {
			errors = append(errors, ValidationError{Field: "log", Message: err.Error()})
		}
	}

	if len(errors) > 0 {
		return errors
	}

	return nil
}

// validateSystem 验证系统配置
func (c *Config) validateSystem() error {
	var errors ValidationErrors

	if c.System.Name == "" {
		errors = append(errors, ValidationError{Field: "system.name", Message: "name is required"})
	}

	if c.System.Version == "" {
		errors = append(errors, ValidationError{Field: "system.version", Message: "version is required"})
	}

	if c.System.Environment == "" {
		errors = append(errors, ValidationError{Field: "system.environment", Message: "environment is required"})
	} else {
		validEnvs := []string{"development", "testing", "staging", "production"}
		if !contains(validEnvs, c.System.Environment) {
			errors = append(errors, ValidationError{
				Field:   "system.environment",
				Message: fmt.Sprintf("environment must be one of: %s", strings.Join(validEnvs, ", ")),
			})
		}
	}

	if c.System.DataDir == "" {
		errors = append(errors, ValidationError{Field: "system.data_dir", Message: "data_dir is required"})
	}

	if c.System.Mode == "" {
		errors = append(errors, ValidationError{Field: "system.mode", Message: "mode is required"})
	} else {
		validModes := []string{"server", "client", "both"}
		if !contains(validModes, c.System.Mode) {
			errors = append(errors, ValidationError{
				Field:   "system.mode",
				Message: fmt.Sprintf("mode must be one of: %s", strings.Join(validModes, ", ")),
			})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateDatabase 验证数据库配置
func (c *Config) validateDatabase() error {
	var errors ValidationErrors

	if c.Database.Path == "" {
		errors = append(errors, ValidationError{Field: "database.path", Message: "path is required"})
	} else {
		// 检查数据库目录是否可写
		dir := filepath.Dir(c.Database.Path)
		if err := os.MkdirAll(dir, 0755); err != nil {
			errors = append(errors, ValidationError{
				Field:   "database.path",
				Message: fmt.Sprintf("cannot create database directory: %v", err),
			})
		}
	}

	if c.Database.MaxOpenConns <= 0 {
		errors = append(errors, ValidationError{Field: "database.max_open_conns", Message: "max_open_conns must be positive"})
	}

	if c.Database.MaxIdleConns < 0 {
		errors = append(errors, ValidationError{Field: "database.max_idle_conns", Message: "max_idle_conns cannot be negative"})
	}

	if c.Database.MaxIdleConns > c.Database.MaxOpenConns {
		errors = append(errors, ValidationError{
			Field:   "database.max_idle_conns",
			Message: "max_idle_conns cannot be greater than max_open_conns",
		})
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateScheduler 验证调度器配置
func (c *Config) validateScheduler() error {
	var errors ValidationErrors

	if c.Scheduler.Mode == "" {
		errors = append(errors, ValidationError{Field: "scheduler.mode", Message: "mode is required"})
	} else {
		validModes := []string{"odd", "even", "both"}
		if !contains(validModes, c.Scheduler.Mode) {
			errors = append(errors, ValidationError{
				Field:   "scheduler.mode",
				Message: fmt.Sprintf("mode must be one of: %s", strings.Join(validModes, ", ")),
			})
		}
	}

	if c.Scheduler.Timezone == "" {
		errors = append(errors, ValidationError{Field: "scheduler.timezone", Message: "timezone is required"})
	} else {
		if _, err := time.LoadLocation(c.Scheduler.Timezone); err != nil {
			errors = append(errors, ValidationError{
				Field:   "scheduler.timezone",
				Message: fmt.Sprintf("invalid timezone: %v", err),
			})
		}
	}

	if c.Scheduler.StartHour < 0 || c.Scheduler.StartHour > 23 {
		errors = append(errors, ValidationError{Field: "scheduler.start_hour", Message: "start_hour must be between 0 and 23"})
	}

	if c.Scheduler.EndHour < 0 || c.Scheduler.EndHour > 23 {
		errors = append(errors, ValidationError{Field: "scheduler.end_hour", Message: "end_hour must be between 0 and 23"})
	}

	if c.Scheduler.MaxConcurrent <= 0 {
		errors = append(errors, ValidationError{Field: "scheduler.max_concurrent", Message: "max_concurrent must be positive"})
	}

	if c.Scheduler.RetryAttempts < 0 {
		errors = append(errors, ValidationError{Field: "scheduler.retry_attempts", Message: "retry_attempts cannot be negative"})
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateServers 验证服务器配置
func (c *Config) validateServers() error {
	var errors ValidationErrors

	if len(c.Servers) == 0 {
		errors = append(errors, ValidationError{Field: "servers", Message: "at least one server is required"})
		return errors
	}

	nameMap := make(map[string]bool)
	ipMap := make(map[string]bool)

	for i, server := range c.Servers {
		prefix := fmt.Sprintf("servers[%d]", i)

		if server.Name == "" {
			errors = append(errors, ValidationError{Field: prefix + ".name", Message: "name is required"})
		} else {
			if nameMap[server.Name] {
				errors = append(errors, ValidationError{Field: prefix + ".name", Message: "duplicate server name"})
			}
			nameMap[server.Name] = true
		}

		if server.IP == "" {
			errors = append(errors, ValidationError{Field: prefix + ".ip", Message: "ip is required"})
		} else {
			if net.ParseIP(server.IP) == nil {
				errors = append(errors, ValidationError{Field: prefix + ".ip", Message: "invalid IP address"})
			} else {
				ipKey := fmt.Sprintf("%s:%d", server.IP, server.Port)
				if ipMap[ipKey] {
					errors = append(errors, ValidationError{Field: prefix + ".ip", Message: "duplicate server IP:port"})
				}
				ipMap[ipKey] = true
			}
		}

		if server.Port <= 0 || server.Port > 65535 {
			errors = append(errors, ValidationError{Field: prefix + ".port", Message: "port must be between 1 and 65535"})
		}

		if server.Priority < 0 {
			errors = append(errors, ValidationError{Field: prefix + ".priority", Message: "priority cannot be negative"})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validatePeer 验证对端配置
func (c *Config) validatePeer() error {
	var errors ValidationErrors

	if c.Peer.Enabled {
		if c.Peer.IP == "" {
			errors = append(errors, ValidationError{Field: "peer.ip", Message: "ip is required when peer is enabled"})
		} else if net.ParseIP(c.Peer.IP) == nil {
			errors = append(errors, ValidationError{Field: "peer.ip", Message: "invalid IP address"})
		}

		if c.Peer.Port <= 0 || c.Peer.Port > 65535 {
			errors = append(errors, ValidationError{Field: "peer.port", Message: "port must be between 1 and 65535"})
		}

		if c.Peer.Username == "" {
			errors = append(errors, ValidationError{Field: "peer.username", Message: "username is required when peer is enabled"})
		}

		if c.Peer.Password == "" {
			errors = append(errors, ValidationError{Field: "peer.password", Message: "password is required when peer is enabled"})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateTest 验证测试配置
func (c *Config) validateTest() error {
	var errors ValidationErrors

	if c.Test.Duration <= 0 {
		errors = append(errors, ValidationError{Field: "test.duration", Message: "duration must be positive"})
	}

	if c.Test.Parallel <= 0 {
		errors = append(errors, ValidationError{Field: "test.parallel", Message: "parallel must be positive"})
	}

	if c.Test.Protocol == "" {
		errors = append(errors, ValidationError{Field: "test.protocol", Message: "protocol is required"})
	} else {
		validProtocols := []string{"tcp", "udp", "both"}
		if !contains(validProtocols, c.Test.Protocol) {
			errors = append(errors, ValidationError{
				Field:   "test.protocol",
				Message: fmt.Sprintf("protocol must be one of: %s", strings.Join(validProtocols, ", ")),
			})
		}
	}

	if c.Test.MaxRetries < 0 {
		errors = append(errors, ValidationError{Field: "test.max_retries", Message: "max_retries cannot be negative"})
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateWeb 验证Web配置
func (c *Config) validateWeb() error {
	var errors ValidationErrors

	if c.Web.Enabled {
		if c.Web.Port <= 0 || c.Web.Port > 65535 {
			errors = append(errors, ValidationError{Field: "web.port", Message: "port must be between 1 and 65535"})
		}

		if c.Web.Host == "" {
			errors = append(errors, ValidationError{Field: "web.host", Message: "host is required when web is enabled"})
		}

		if c.Web.TLS.Enabled {
			if c.Web.TLS.CertFile == "" {
				errors = append(errors, ValidationError{Field: "web.tls.cert_file", Message: "cert_file is required when TLS is enabled"})
			}
			if c.Web.TLS.KeyFile == "" {
				errors = append(errors, ValidationError{Field: "web.tls.key_file", Message: "key_file is required when TLS is enabled"})
			}
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateAPI 验证API配置
func (c *Config) validateAPI() error {
	var errors ValidationErrors

	if c.API.Enabled {
		if c.API.Port <= 0 || c.API.Port > 65535 {
			errors = append(errors, ValidationError{Field: "api.port", Message: "port must be between 1 and 65535"})
		}

		if c.API.Host == "" {
			errors = append(errors, ValidationError{Field: "api.host", Message: "host is required when API is enabled"})
		}

		if c.API.RateLimit < 0 {
			errors = append(errors, ValidationError{Field: "api.rate_limit", Message: "rate_limit cannot be negative"})
		}

		if c.API.TLS.Enabled {
			if c.API.TLS.CertFile == "" {
				errors = append(errors, ValidationError{Field: "api.tls.cert_file", Message: "cert_file is required when TLS is enabled"})
			}
			if c.API.TLS.KeyFile == "" {
				errors = append(errors, ValidationError{Field: "api.tls.key_file", Message: "key_file is required when TLS is enabled"})
			}
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateSync 验证同步配置
func (c *Config) validateSync() error {
	var errors ValidationErrors

	if c.Sync.Enabled {
		if c.Sync.BatchSize <= 0 {
			errors = append(errors, ValidationError{Field: "sync.batch_size", Message: "batch_size must be positive"})
		}

		if c.Sync.MaxRetries < 0 {
			errors = append(errors, ValidationError{Field: "sync.max_retries", Message: "max_retries cannot be negative"})
		}
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// validateLog 验证日志配置
func (c *Config) validateLog() error {
	var errors ValidationErrors

	if c.Log.Level == "" {
		errors = append(errors, ValidationError{Field: "log.level", Message: "level is required"})
	} else {
		validLevels := []string{"debug", "info", "warn", "error", "fatal", "panic"}
		if !contains(validLevels, c.Log.Level) {
			errors = append(errors, ValidationError{
				Field:   "log.level",
				Message: fmt.Sprintf("level must be one of: %s", strings.Join(validLevels, ", ")),
			})
		}
	}

	if c.Log.Format == "" {
		errors = append(errors, ValidationError{Field: "log.format", Message: "format is required"})
	} else {
		validFormats := []string{"json", "text"}
		if !contains(validFormats, c.Log.Format) {
			errors = append(errors, ValidationError{
				Field:   "log.format",
				Message: fmt.Sprintf("format must be one of: %s", strings.Join(validFormats, ", ")),
			})
		}
	}

	if c.Log.Output == "" {
		errors = append(errors, ValidationError{Field: "log.output", Message: "output is required"})
	} else {
		validOutputs := []string{"stdout", "stderr", "file"}
		if !contains(validOutputs, c.Log.Output) {
			errors = append(errors, ValidationError{
				Field:   "log.output",
				Message: fmt.Sprintf("output must be one of: %s", strings.Join(validOutputs, ", ")),
			})
		}

		if c.Log.Output == "file" && c.Log.File == "" {
			errors = append(errors, ValidationError{Field: "log.file", Message: "file is required when output is 'file'"})
		}
	}

	if c.Log.MaxSize <= 0 {
		errors = append(errors, ValidationError{Field: "log.max_size", Message: "max_size must be positive"})
	}

	if c.Log.MaxBackups < 0 {
		errors = append(errors, ValidationError{Field: "log.max_backups", Message: "max_backups cannot be negative"})
	}

	if c.Log.MaxAge < 0 {
		errors = append(errors, ValidationError{Field: "log.max_age", Message: "max_age cannot be negative"})
	}

	if len(errors) > 0 {
		return errors
	}
	return nil
}

// contains 检查字符串切片是否包含指定值
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// isValidName 检查名称是否有效
func isValidName(name string) bool {
	// 名称只能包含字母、数字、连字符和下划线
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, name)
	return matched
}
