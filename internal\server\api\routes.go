package api

import (
	"github.com/gin-gonic/gin"
)

// SetupRouter 设置 Gin 路由器，包含所有 API 路由。(Sets up the Gin router with all API routes.)
func SetupRouter() *gin.Engine {
	router := gin.Default()

	// 应用中间件 (Apply middleware)
	router.Use(LoggerMiddleware())

	// 公共路由 (Public routes)
	public := router.Group("/api/v1")
	{
		public.GET("/health", HealthCheckHandler)
		public.POST("/example", ExampleHandler) // 用于数据验证示例的新路由 (New route for data validation example)
	}

	// 认证路由（示例） (Authentication routes (example))
	// authenticated := router.Group("/api/v1")
	// authenticated.Use(AuthMiddleware()) // 假设存在 AuthMiddleware (Assuming AuthMiddleware exists)
	// {
	// 	authenticated.GET("/data", GetDataHandler)
	// }

	return router
}
