package api

import (
	"github.com/gin-gonic/gin"
)

// SetupRouter sets up the Gin router with all API routes.
func SetupRouter() *gin.Engine {
	router := gin.Default()

	// Apply middleware
	router.Use(LoggerMiddleware())

	// Public routes
	public := router.Group("/api/v1")
	{
		public.GET("/health", HealthCheckHandler)
		public.POST("/example", ExampleHandler) // New route for data validation example
	}

	// Authenticated routes (example)
	// authenticated := router.Group("/api/v1")
	// authenticated.Use(AuthMiddleware()) // Assuming an AuthMiddleware exists
	// {
	// 	authenticated.GET("/data", GetDataHandler)
	// }

	return router
}
