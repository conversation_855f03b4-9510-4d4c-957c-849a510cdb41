package websocket

type Hub struct {
	// 已注册的客户端。(Registered clients.)
	clients map[*Client]bool

	// 来自客户端的入站消息。(Inbound messages from clients.)
	broadcast chan []byte

	// 来自客户端的注册请求。(Registration requests from clients.)
	register chan *Client

	// 来自客户端的取消注册请求。(Unregistration requests from clients.)
	unregister chan *Client
}

func NewHub() *Hub {
	return &Hub{
		broadcast:  make(chan []byte),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		clients:    make(map[*Client]bool),
	}
}

func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client] = true
		case client := <-h.unregister:
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
			}
		case message := <-h.broadcast:
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}
		}
	}
}
