# 服务器监控系统

一个功能完整的服务器监控和网络性能测试系统，支持实时监控、自动化测试、Web界面管理等功能。

## 🚀 主要功能

### 核心功能
- **实时系统监控**: CPU、内存、磁盘、网络使用率监控
- **网络性能测试**: 基于iPerf3的自动化网络性能测试
- **Web管理界面**: 现代化响应式Web界面
- **RESTful API**: 完整的API接口支持
- **数据持久化**: SQLite数据库存储历史数据
- **远程客户端**: HTTP客户端支持远程监控

### 高级功能
- **自动化调度**: 定时执行性能测试
- **多服务器管理**: 支持管理多个监控目标
- **实时数据更新**: 自动刷新监控数据
- **跨平台支持**: Windows、Linux、macOS
- **配置管理**: YAML配置文件，支持环境变量覆盖

## 📦 项目结构

```
server-monitor/
├── cmd/                    # 可执行程序
│   ├── api-test/          # API服务器测试程序
│   ├── client-test/       # 客户端测试程序
│   ├── iperf-test/        # iPerf3测试程序
│   ├── monitor-test/      # 系统监控测试程序
│   └── web-test/          # Web界面测试程序
├── internal/              # 内部包
│   ├── api/               # API服务器
│   ├── client/            # HTTP客户端
│   ├── config/            # 配置管理
│   ├── database/          # 数据库操作
│   ├── iperf/             # iPerf3测试模块
│   ├── monitor/           # 系统监控模块
│   └── web/               # Web服务器
├── web/                   # Web资源
│   └── templates/         # HTML模板
├── configs/               # 配置文件
├── scripts/               # 构建脚本
└── docs/                  # 文档
```

## 🛠️ 技术栈

- **后端**: Go 1.21+
- **数据库**: SQLite + GORM
- **Web框架**: Gin
- **前端**: HTML5 + CSS3 + JavaScript
- **监控**: gopsutil
- **网络测试**: iPerf3
- **配置**: Viper (YAML)

## 📋 系统要求

- Go 1.21 或更高版本
- iPerf3 (用于网络性能测试)
- SQLite3

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd server-monitor
```

### 2. 安装依赖
```bash
go mod download
```

### 3. 安装iPerf3
```bash
# Ubuntu/Debian
sudo apt-get install iperf3

# CentOS/RHEL
sudo yum install iperf3

# macOS
brew install iperf3

# Windows
# 从 https://iperf.fr/iperf-download.php 下载
```

### 4. 运行系统监控
```bash
go run ./cmd/monitor-test --once
```

### 5. 启动Web界面
```bash
go run ./cmd/web-test --port 8080
```
访问 http://localhost:8080

### 6. 启动API服务器
```bash
go run ./cmd/api-test --port 8443
```
API文档: http://localhost:8443/api/v1/health

## 🔧 配置说明

主配置文件位于 `configs/config.yaml`，支持以下配置项：

```yaml
# 系统配置
system:
  name: "Server Monitor"
  environment: "production"
  
# 数据库配置
database:
  path: "./data/monitor.db"
  max_open_conns: 25
  
# API配置
api:
  enabled: true
  port: 8443
  host: "0.0.0.0"
  
# Web配置
web:
  enabled: true
  port: 8080
  host: "0.0.0.0"
  
# iPerf3配置
iperf:
  enabled: true
  test_interval: 60      # 测试间隔(分钟)
  test_duration: 30      # 测试时长(秒)
  parallel_streams: 4    # 并行流数量
```

## 📊 使用示例

### 系统监控
```bash
# 单次监控
go run ./cmd/monitor-test --once

# 持续监控
go run ./cmd/monitor-test --interval 5s

# 详细信息
go run ./cmd/monitor-test --once --detailed
```

### iPerf3测试
```bash
# 检查iPerf3环境
go run ./cmd/iperf-test --mode check

# 单次测试
go run ./cmd/iperf-test --mode single --server *************

# 调度模式
go run ./cmd/iperf-test --mode schedule
```

### 客户端操作
```bash
# 健康检查
go run ./cmd/client-test --cmd health

# 获取系统信息
go run ./cmd/client-test --cmd system

# 列出服务器
go run ./cmd/client-test --cmd servers

# 创建服务器
go run ./cmd/client-test --cmd create --name "test" --ip "*************"
```

## 🌐 API接口

### 系统监控API
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/system/info` - 系统信息
- `GET /api/v1/system/cpu` - CPU统计
- `GET /api/v1/system/memory` - 内存信息
- `GET /api/v1/system/disk` - 磁盘统计
- `GET /api/v1/system/network` - 网络统计

### 服务器管理API
- `GET /api/v1/servers` - 服务器列表
- `POST /api/v1/servers` - 创建服务器
- `GET /api/v1/servers/{id}` - 获取服务器
- `PUT /api/v1/servers/{id}` - 更新服务器
- `DELETE /api/v1/servers/{id}` - 删除服务器

### 统计信息API
- `GET /api/v1/stats/summary` - 统计摘要
- `GET /api/v1/stats/servers` - 服务器统计

## 🏗️ 构建部署

### 本地构建
```bash
# 构建所有程序
make build

# 构建特定程序
go build -o bin/monitor ./cmd/monitor-test
go build -o bin/web ./cmd/web-test
go build -o bin/api ./cmd/api-test
```

### 跨平台构建
```bash
# Windows
GOOS=windows GOARCH=amd64 go build -o bin/monitor.exe ./cmd/monitor-test

# Linux
GOOS=linux GOARCH=amd64 go build -o bin/monitor ./cmd/monitor-test

# macOS
GOOS=darwin GOARCH=amd64 go build -o bin/monitor ./cmd/monitor-test
```

## 📈 项目状态

- ✅ 核心架构: 100% 完成
- ✅ 基础功能: 100% 完成  
- 🚧 高级功能: 60% 完成
- 📊 整体进度: 85% 完成

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [gopsutil](https://github.com/shirou/gopsutil) - 系统信息获取
- [Gin](https://github.com/gin-gonic/gin) - Web框架
- [GORM](https://gorm.io/) - ORM框架
- [Viper](https://github.com/spf13/viper) - 配置管理
- [iPerf3](https://iperf.fr/) - 网络性能测试工具
