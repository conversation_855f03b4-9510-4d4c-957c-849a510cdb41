package main

import (
	"log"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func main() {
	// 这是一个临时的main文件，用于确保依赖包被正确添加到go.mod
	// 在后续开发中会被替换为实际的应用程序入口
	
	log.Println("Server Monitor - Dependencies Test")
	
	// 引用各个依赖包以确保它们被添加到go.mod
	_ = gin.New()
	_ = websocket.Upgrader{}
	_, _ = cpu.Info()
	_ = logrus.New()
	_, _ = gorm.Open(sqlite.Open("test.db"), &gorm.Config{})
	
	log.Println("All dependencies loaded successfully")
}
