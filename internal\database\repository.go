package database

import (
	"database/sql"
	"time"
)

// Repository 数据访问接口
type Repository interface {
	// Server operations
	CreateServer(server *Server) error
	GetServer(id int) (*Server, error)
	GetServerByName(name string) (*Server, error)
	UpdateServer(server *Server) error
	DeleteServer(id int) error
	ListServers(active bool) ([]*Server, error)

	// HourlyResult operations
	CreateHourlyResult(result *HourlyResult) error
	GetHourlyResult(serverID int, testHour time.Time) (*HourlyResult, error)
	UpdateHourlyResult(result *HourlyResult) error
	DeleteHourlyResult(id int) error
	ListHourlyResults(serverID int, startTime, endTime time.Time) ([]*HourlyResult, error)

	// SyncStatus operations
	CreateSyncStatus(status *SyncStatus) error
	GetSyncStatus(peerIP string) (*SyncStatus, error)
	UpdateSyncStatus(status *SyncStatus) error
	DeleteSyncStatus(peerIP string) error
	ListSyncStatus() ([]*SyncStatus, error)

	// SystemConfig operations
	SetConfig(key, value, description, configType string, isSystem bool) error
	GetConfig(key string) (*SystemConfig, error)
	DeleteConfig(key string) error
	ListConfigs(isSystem bool) ([]*SystemConfig, error)
}

// SQLiteRepository SQLite数据访问实现
type SQLiteRepository struct {
	db *Database
}

// NewRepository 创建新的数据访问实例
func NewRepository(db *Database) Repository {
	return &SQLiteRepository{db: db}
}

// Server operations

func (r *SQLiteRepository) CreateServer(server *Server) error {
	if err := server.Validate(); err != nil {
		return err
	}

	server.CreatedAt = time.Now()
	server.UpdatedAt = time.Now()

	query := `
		INSERT INTO servers (name, ip, port, location, provider, is_active, last_seen, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := r.db.GetDB().Exec(query,
		server.Name, server.IP, server.Port, server.Location, server.Provider,
		server.IsActive, server.LastSeen, server.CreatedAt, server.UpdatedAt)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	server.ID = int(id)
	return nil
}

func (r *SQLiteRepository) GetServer(id int) (*Server, error) {
	query := `
		SELECT id, name, ip, port, location, provider, is_active, last_seen, created_at, updated_at
		FROM servers WHERE id = ?
	`

	var server Server
	err := r.db.GetDB().QueryRow(query, id).Scan(
		&server.ID, &server.Name, &server.IP, &server.Port, &server.Location,
		&server.Provider, &server.IsActive, &server.LastSeen, &server.CreatedAt, &server.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}

	return &server, nil
}

func (r *SQLiteRepository) GetServerByName(name string) (*Server, error) {
	query := `
		SELECT id, name, ip, port, location, provider, is_active, last_seen, created_at, updated_at
		FROM servers WHERE name = ?
	`

	var server Server
	err := r.db.GetDB().QueryRow(query, name).Scan(
		&server.ID, &server.Name, &server.IP, &server.Port, &server.Location,
		&server.Provider, &server.IsActive, &server.LastSeen, &server.CreatedAt, &server.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}

	return &server, nil
}

func (r *SQLiteRepository) UpdateServer(server *Server) error {
	if err := server.Validate(); err != nil {
		return err
	}

	server.UpdatedAt = time.Now()

	query := `
		UPDATE servers 
		SET name = ?, ip = ?, port = ?, location = ?, provider = ?, 
		    is_active = ?, last_seen = ?, updated_at = ?
		WHERE id = ?
	`

	result, err := r.db.GetDB().Exec(query,
		server.Name, server.IP, server.Port, server.Location, server.Provider,
		server.IsActive, server.LastSeen, server.UpdatedAt, server.ID)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrRecordNotFound
	}

	return nil
}

func (r *SQLiteRepository) DeleteServer(id int) error {
	query := "DELETE FROM servers WHERE id = ?"

	result, err := r.db.GetDB().Exec(query, id)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrRecordNotFound
	}

	return nil
}

func (r *SQLiteRepository) ListServers(active bool) ([]*Server, error) {
	var query string
	var args []interface{}

	if active {
		query = `
			SELECT id, name, ip, port, location, provider, is_active, last_seen, created_at, updated_at
			FROM servers WHERE is_active = 1 ORDER BY name
		`
	} else {
		query = `
			SELECT id, name, ip, port, location, provider, is_active, last_seen, created_at, updated_at
			FROM servers ORDER BY name
		`
	}

	rows, err := r.db.GetDB().Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var servers []*Server
	for rows.Next() {
		var server Server
		err := rows.Scan(
			&server.ID, &server.Name, &server.IP, &server.Port, &server.Location,
			&server.Provider, &server.IsActive, &server.LastSeen, &server.CreatedAt, &server.UpdatedAt)
		if err != nil {
			return nil, err
		}
		servers = append(servers, &server)
	}

	return servers, rows.Err()
}

// HourlyResult operations

func (r *SQLiteRepository) CreateHourlyResult(result *HourlyResult) error {
	if err := result.Validate(); err != nil {
		return err
	}

	result.CreatedAt = time.Now()

	query := `
		INSERT INTO hourly_results (
			server_id, test_hour, upload_speed, download_speed, latency, jitter,
			packet_loss, test_duration, test_type, status, error_message, created_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	dbResult, err := r.db.GetDB().Exec(query,
		result.ServerID, result.TestHour, result.UploadSpeed, result.DownloadSpeed,
		result.Latency, result.Jitter, result.PacketLoss, result.TestDuration,
		result.TestType, result.Status, result.ErrorMessage, result.CreatedAt)
	if err != nil {
		return err
	}

	id, err := dbResult.LastInsertId()
	if err != nil {
		return err
	}

	result.ID = int(id)
	return nil
}

func (r *SQLiteRepository) GetHourlyResult(serverID int, testHour time.Time) (*HourlyResult, error) {
	query := `
		SELECT id, server_id, test_hour, upload_speed, download_speed, latency, jitter,
		       packet_loss, test_duration, test_type, status, error_message, created_at
		FROM hourly_results WHERE server_id = ? AND test_hour = ?
	`

	var result HourlyResult
	err := r.db.GetDB().QueryRow(query, serverID, testHour).Scan(
		&result.ID, &result.ServerID, &result.TestHour, &result.UploadSpeed,
		&result.DownloadSpeed, &result.Latency, &result.Jitter, &result.PacketLoss,
		&result.TestDuration, &result.TestType, &result.Status, &result.ErrorMessage,
		&result.CreatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}

	return &result, nil
}

func (r *SQLiteRepository) UpdateHourlyResult(result *HourlyResult) error {
	if err := result.Validate(); err != nil {
		return err
	}

	query := `
		UPDATE hourly_results 
		SET upload_speed = ?, download_speed = ?, latency = ?, jitter = ?,
		    packet_loss = ?, test_duration = ?, test_type = ?, status = ?, error_message = ?
		WHERE id = ?
	`

	dbResult, err := r.db.GetDB().Exec(query,
		result.UploadSpeed, result.DownloadSpeed, result.Latency, result.Jitter,
		result.PacketLoss, result.TestDuration, result.TestType, result.Status,
		result.ErrorMessage, result.ID)
	if err != nil {
		return err
	}

	rowsAffected, err := dbResult.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrRecordNotFound
	}

	return nil
}

func (r *SQLiteRepository) DeleteHourlyResult(id int) error {
	query := "DELETE FROM hourly_results WHERE id = ?"

	result, err := r.db.GetDB().Exec(query, id)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrRecordNotFound
	}

	return nil
}

func (r *SQLiteRepository) ListHourlyResults(serverID int, startTime, endTime time.Time) ([]*HourlyResult, error) {
	query := `
		SELECT id, server_id, test_hour, upload_speed, download_speed, latency, jitter,
		       packet_loss, test_duration, test_type, status, error_message, created_at
		FROM hourly_results 
		WHERE server_id = ? AND test_hour BETWEEN ? AND ?
		ORDER BY test_hour DESC
	`

	rows, err := r.db.GetDB().Query(query, serverID, startTime, endTime)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []*HourlyResult
	for rows.Next() {
		var result HourlyResult
		err := rows.Scan(
			&result.ID, &result.ServerID, &result.TestHour, &result.UploadSpeed,
			&result.DownloadSpeed, &result.Latency, &result.Jitter, &result.PacketLoss,
			&result.TestDuration, &result.TestType, &result.Status, &result.ErrorMessage,
			&result.CreatedAt)
		if err != nil {
			return nil, err
		}
		results = append(results, &result)
	}

	return results, rows.Err()
}

// SyncStatus operations

func (r *SQLiteRepository) CreateSyncStatus(status *SyncStatus) error {
	if err := status.Validate(); err != nil {
		return err
	}

	status.CreatedAt = time.Now()
	status.UpdatedAt = time.Now()

	query := `
		INSERT INTO sync_status (peer_ip, last_sync_time, sync_status, records_synced, last_sync_error, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	result, err := r.db.GetDB().Exec(query,
		status.PeerIP, status.LastSyncTime, status.SyncStatus, status.RecordsSynced,
		status.LastSyncError, status.CreatedAt, status.UpdatedAt)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	status.ID = int(id)
	return nil
}

func (r *SQLiteRepository) GetSyncStatus(peerIP string) (*SyncStatus, error) {
	query := `
		SELECT id, peer_ip, last_sync_time, sync_status, records_synced, last_sync_error, created_at, updated_at
		FROM sync_status WHERE peer_ip = ?
	`

	var status SyncStatus
	err := r.db.GetDB().QueryRow(query, peerIP).Scan(
		&status.ID, &status.PeerIP, &status.LastSyncTime, &status.SyncStatus,
		&status.RecordsSynced, &status.LastSyncError, &status.CreatedAt, &status.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}

	return &status, nil
}

func (r *SQLiteRepository) UpdateSyncStatus(status *SyncStatus) error {
	if err := status.Validate(); err != nil {
		return err
	}

	status.UpdatedAt = time.Now()

	query := `
		UPDATE sync_status
		SET last_sync_time = ?, sync_status = ?, records_synced = ?, last_sync_error = ?, updated_at = ?
		WHERE peer_ip = ?
	`

	result, err := r.db.GetDB().Exec(query,
		status.LastSyncTime, status.SyncStatus, status.RecordsSynced,
		status.LastSyncError, status.UpdatedAt, status.PeerIP)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrRecordNotFound
	}

	return nil
}

func (r *SQLiteRepository) DeleteSyncStatus(peerIP string) error {
	query := "DELETE FROM sync_status WHERE peer_ip = ?"

	result, err := r.db.GetDB().Exec(query, peerIP)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrRecordNotFound
	}

	return nil
}

func (r *SQLiteRepository) ListSyncStatus() ([]*SyncStatus, error) {
	query := `
		SELECT id, peer_ip, last_sync_time, sync_status, records_synced, last_sync_error, created_at, updated_at
		FROM sync_status ORDER BY peer_ip
	`

	rows, err := r.db.GetDB().Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var statuses []*SyncStatus
	for rows.Next() {
		var status SyncStatus
		err := rows.Scan(
			&status.ID, &status.PeerIP, &status.LastSyncTime, &status.SyncStatus,
			&status.RecordsSynced, &status.LastSyncError, &status.CreatedAt, &status.UpdatedAt)
		if err != nil {
			return nil, err
		}
		statuses = append(statuses, &status)
	}

	return statuses, rows.Err()
}

// SystemConfig operations

func (r *SQLiteRepository) SetConfig(key, value, description, configType string, isSystem bool) error {
	if key == "" {
		return ErrInvalidConfigKey
	}
	if configType == "" {
		configType = ConfigTypeString
	}

	now := time.Now()

	// 尝试更新现有配置
	updateQuery := `
		UPDATE system_config
		SET config_value = ?, description = ?, config_type = ?, is_system = ?, updated_at = ?
		WHERE config_key = ?
	`

	result, err := r.db.GetDB().Exec(updateQuery, value, description, configType, isSystem, now, key)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	// 如果没有更新任何行，则插入新配置
	if rowsAffected == 0 {
		insertQuery := `
			INSERT INTO system_config (config_key, config_value, description, config_type, is_system, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?)
		`
		_, err = r.db.GetDB().Exec(insertQuery, key, value, description, configType, isSystem, now, now)
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *SQLiteRepository) GetConfig(key string) (*SystemConfig, error) {
	query := `
		SELECT id, config_key, config_value, description, config_type, is_system, created_at, updated_at
		FROM system_config WHERE config_key = ?
	`

	var config SystemConfig
	err := r.db.GetDB().QueryRow(query, key).Scan(
		&config.ID, &config.ConfigKey, &config.ConfigValue, &config.Description,
		&config.ConfigType, &config.IsSystem, &config.CreatedAt, &config.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}

	return &config, nil
}

func (r *SQLiteRepository) DeleteConfig(key string) error {
	query := "DELETE FROM system_config WHERE config_key = ?"

	result, err := r.db.GetDB().Exec(query, key)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrRecordNotFound
	}

	return nil
}

func (r *SQLiteRepository) ListConfigs(isSystem bool) ([]*SystemConfig, error) {
	var query string
	var args []interface{}

	if isSystem {
		query = `
			SELECT id, config_key, config_value, description, config_type, is_system, created_at, updated_at
			FROM system_config WHERE is_system = 1 ORDER BY config_key
		`
	} else {
		query = `
			SELECT id, config_key, config_value, description, config_type, is_system, created_at, updated_at
			FROM system_config ORDER BY config_key
		`
	}

	rows, err := r.db.GetDB().Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var configs []*SystemConfig
	for rows.Next() {
		var config SystemConfig
		err := rows.Scan(
			&config.ID, &config.ConfigKey, &config.ConfigValue, &config.Description,
			&config.ConfigType, &config.IsSystem, &config.CreatedAt, &config.UpdatedAt)
		if err != nil {
			return nil, err
		}
		configs = append(configs, &config)
	}

	return configs, rows.Err()
}
