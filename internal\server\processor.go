package server

import (
	"context"
	"fmt"
	"log"

	"your_project/internal/database" // Assuming database operations are here
	"your_project/internal/models"   // Assuming models are defined here
)

// DataProcessor defines the interface for processing incoming data.
type DataProcessor interface {
	ProcessData(ctx context.Context, data models.SystemInfo) error
}

// processor implements the DataProcessor interface.
type processor struct {
	dbRepo database.Repository // Assuming a generic repository interface
}

// NewDataProcessor creates a new data processor.
func NewDataProcessor(dbRepo database.Repository) DataProcessor {
	return &processor{
		dbRepo: dbRepo,
	}
}

// ProcessData processes incoming system information data.
func (p *processor) ProcessData(ctx context.Context, data models.SystemInfo) error {
	log.Printf("Processing data for server %s: CPU %.2f%%, Mem %.2f%%", data.ServerID, data.CPUUsage, data.MemoryUsage)

	// 1. 数据存储逻辑
	if err := p.dbRepo.CreateSystemInfo(ctx, data); err != nil {
		return fmt.Errorf("failed to store system info: %w", err)
	}
	log.Printf("System info for server %s stored successfully.", data.ServerID)

	// 2. 告警检测 (Placeholder)
	// In a real scenario, you would have a more sophisticated alarm detection system.
	if data.CPUUsage > 90.0 {
		log.Printf("ALERT: High CPU usage for server %s: %.2f%%", data.ServerID, data.CPUUsage)
		// Trigger alert notification (e.g., email, SMS, webhook)
		// p.dbRepo.CreateAlert(ctx, models.Alert{
		// 	ServerID: data.ServerID,
		// 	Type: "CPU_HIGH",
		// 	Message: fmt.Sprintf("CPU usage exceeded 90%%: %.2f%%", data.CPUUsage),
		// 	Timestamp: time.Now(),
		// })
	}

	// 3. 数据聚合 (Placeholder)
	// In a real scenario, you might aggregate data for historical trends, dashboards, etc.
	// For example, calculating hourly averages or daily summaries.
	// This could involve another repository method or a separate aggregation service.
	log.Printf("Data aggregation for server %s completed.", data.ServerID)

	return nil
}
