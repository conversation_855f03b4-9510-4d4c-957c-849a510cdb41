package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"time"

	"server-monitor/internal/client"
	"server-monitor/internal/config"
)

func main() {
	var (
		configFile = flag.String("config", "./configs/config.yaml", "配置文件路径")
		serverHost = flag.String("host", "127.0.0.1", "服务器地址")
		serverPort = flag.Int("port", 8443, "服务器端口")
		command    = flag.String("cmd", "health", "执行命令: health, system, servers, create, get, update, delete")
		serverID   = flag.Int("id", 0, "服务器ID(用于get/update/delete)")
		serverName = flag.String("name", "", "服务器名称(用于create/update)")
		serverIP   = flag.String("ip", "", "服务器IP(用于create/update)")
		serverLoc  = flag.String("location", "", "服务器位置(用于create/update)")
	)
	flag.Parse()

	fmt.Printf("远程监控客户端测试程序\n")
	fmt.Printf("配置文件: %s\n", *configFile)
	fmt.Printf("目标服务器: %s:%d\n", *serverHost, *serverPort)
	fmt.Printf("执行命令: %s\n", *command)
	fmt.Println("---")

	// 加载配置
	configManager := config.NewManager(*configFile)
	if err := configManager.Load(); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	cfg := configManager.Get()
	
	// 覆盖API配置
	cfg.API.Host = *serverHost
	cfg.API.Port = *serverPort

	fmt.Printf("配置加载成功\n")

	// 创建客户端
	apiClient := client.NewClient(cfg)

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 执行命令
	switch *command {
	case "health":
		runHealthCheck(ctx, apiClient)
	case "system":
		runSystemInfo(ctx, apiClient)
	case "servers":
		runListServers(ctx, apiClient)
	case "create":
		runCreateServer(ctx, apiClient, *serverName, *serverIP, *serverLoc)
	case "get":
		runGetServer(ctx, apiClient, *serverID)
	case "update":
		runUpdateServer(ctx, apiClient, *serverID, *serverName, *serverIP, *serverLoc)
	case "delete":
		runDeleteServer(ctx, apiClient, *serverID)
	default:
		log.Fatalf("未知命令: %s", *command)
	}
}

// runHealthCheck 健康检查
func runHealthCheck(ctx context.Context, client *client.Client) {
	fmt.Println("=== 健康检查 ===")
	
	err := client.HealthCheck(ctx)
	if err != nil {
		log.Fatalf("健康检查失败: %v", err)
	}
	
	fmt.Println("✅ 服务器健康状态正常")
}

// runSystemInfo 获取系统信息
func runSystemInfo(ctx context.Context, client *client.Client) {
	fmt.Println("=== 系统信息 ===")
	
	info, err := client.GetSystemInfo(ctx)
	if err != nil {
		log.Fatalf("获取系统信息失败: %v", err)
	}
	
	fmt.Printf("主机名: %s\n", info.Hostname)
	fmt.Printf("操作系统: %s (%s)\n", info.OS, info.Platform)
	fmt.Printf("架构: %s\n", info.Architecture)
	fmt.Printf("运行时间: %d 秒\n", info.Uptime)
	fmt.Printf("CPU使用率: %.2f%%\n", info.CPUUsage)
	fmt.Printf("内存使用率: %.2f%% (%s / %s)\n", 
		info.MemoryUsage, 
		formatBytes(info.MemoryUsed), 
		formatBytes(info.MemoryTotal))
	fmt.Printf("磁盘使用率: %.2f%% (%s / %s)\n", 
		info.DiskUsage, 
		formatBytes(info.DiskUsed), 
		formatBytes(info.DiskTotal))
	fmt.Printf("网络接收: %s\n", formatBytes(info.NetworkRx))
	fmt.Printf("网络发送: %s\n", formatBytes(info.NetworkTx))
	if info.LoadAverage > 0 {
		fmt.Printf("负载平均值: %.2f\n", info.LoadAverage)
	}
	fmt.Printf("时间戳: %s\n", time.Unix(info.Timestamp, 0).Format("2006-01-02 15:04:05"))
}

// runListServers 获取服务器列表
func runListServers(ctx context.Context, client *client.Client) {
	fmt.Println("=== 服务器列表 ===")
	
	servers, err := client.ListServers(ctx)
	if err != nil {
		log.Fatalf("获取服务器列表失败: %v", err)
	}
	
	if len(servers) == 0 {
		fmt.Println("暂无服务器")
		return
	}
	
	fmt.Printf("共找到 %d 个服务器:\n\n", len(servers))
	
	for _, server := range servers {
		fmt.Printf("ID: %d\n", server.ID)
		fmt.Printf("名称: %s\n", server.Name)
		fmt.Printf("地址: %s:%d\n", server.IP, server.Port)
		fmt.Printf("位置: %s\n", server.Location)
		fmt.Printf("提供商: %s\n", server.Provider)
		fmt.Printf("状态: %s\n", getStatusText(server.IsActive))
		fmt.Printf("优先级: %d\n", server.Priority)
		if len(server.Tags) > 0 {
			fmt.Printf("标签: %v\n", server.Tags)
		}
		fmt.Println("---")
	}
}

// runCreateServer 创建服务器
func runCreateServer(ctx context.Context, client *client.Client, name, ip, location string) {
	if name == "" || ip == "" {
		log.Fatal("创建服务器需要指定名称和IP地址")
	}
	
	fmt.Println("=== 创建服务器 ===")
	
	server := &client.Server{
		Name:     name,
		IP:       ip,
		Port:     5201,
		Location: location,
		Provider: "测试",
		IsActive: true,
		Priority: 3,
		Tags:     []string{"test"},
	}
	
	created, err := client.CreateServer(ctx, server)
	if err != nil {
		log.Fatalf("创建服务器失败: %v", err)
	}
	
	fmt.Printf("✅ 服务器创建成功\n")
	fmt.Printf("ID: %d\n", created.ID)
	fmt.Printf("名称: %s\n", created.Name)
	fmt.Printf("地址: %s:%d\n", created.IP, created.Port)
}

// runGetServer 获取指定服务器
func runGetServer(ctx context.Context, client *client.Client, id int) {
	if id == 0 {
		log.Fatal("获取服务器需要指定ID")
	}
	
	fmt.Printf("=== 获取服务器 (ID: %d) ===\n", id)
	
	server, err := client.GetServer(ctx, id)
	if err != nil {
		log.Fatalf("获取服务器失败: %v", err)
	}
	
	// 以JSON格式输出详细信息
	data, _ := json.MarshalIndent(server, "", "  ")
	fmt.Println(string(data))
}

// runUpdateServer 更新服务器
func runUpdateServer(ctx context.Context, client *client.Client, id int, name, ip, location string) {
	if id == 0 {
		log.Fatal("更新服务器需要指定ID")
	}
	
	fmt.Printf("=== 更新服务器 (ID: %d) ===\n", id)
	
	// 先获取现有服务器信息
	server, err := client.GetServer(ctx, id)
	if err != nil {
		log.Fatalf("获取服务器失败: %v", err)
	}
	
	// 更新指定字段
	if name != "" {
		server.Name = name
	}
	if ip != "" {
		server.IP = ip
	}
	if location != "" {
		server.Location = location
	}
	
	updated, err := client.UpdateServer(ctx, server)
	if err != nil {
		log.Fatalf("更新服务器失败: %v", err)
	}
	
	fmt.Printf("✅ 服务器更新成功\n")
	fmt.Printf("名称: %s\n", updated.Name)
	fmt.Printf("地址: %s:%d\n", updated.IP, updated.Port)
	fmt.Printf("位置: %s\n", updated.Location)
}

// runDeleteServer 删除服务器
func runDeleteServer(ctx context.Context, client *client.Client, id int) {
	if id == 0 {
		log.Fatal("删除服务器需要指定ID")
	}
	
	fmt.Printf("=== 删除服务器 (ID: %d) ===\n", id)
	
	err := client.DeleteServer(ctx, id)
	if err != nil {
		log.Fatalf("删除服务器失败: %v", err)
	}
	
	fmt.Printf("✅ 服务器删除成功\n")
}

// formatBytes 格式化字节数
func formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// getStatusText 获取状态文本
func getStatusText(isActive bool) string {
	if isActive {
		return "活跃"
	}
	return "停用"
}
