package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/monitor"
	"server-monitor/internal/websocket"
)

func main() {
	var (
		configFile = flag.String("config", "./configs/config.yaml", "配置文件路径")
		port       = flag.String("port", "8090", "WebSocket服务端口")
		host       = flag.String("host", "127.0.0.1", "WebSocket服务监听地址")
	)
	flag.Parse()

	fmt.Printf("WebSocket实时通信测试程序\n")
	fmt.Printf("配置文件: %s\n", *configFile)
	fmt.Printf("监听地址: %s:%s\n", *host, *port)
	fmt.Println("---")

	// 加载配置
	configManager := config.NewManager(*configFile)
	if err := configManager.Load(); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	cfg := configManager.Get()
	fmt.Printf("配置加载成功\n")

	// 初始化数据库
	dbConfig := &database.Config{
		DatabasePath:     cfg.Database.Path,
		MaxOpenConns:     cfg.Database.MaxOpenConns,
		MaxIdleConns:     cfg.Database.MaxIdleConns,
		ConnMaxLifetime:  cfg.Database.ConnMaxLifetime,
		ConnMaxIdleTime:  cfg.Database.ConnMaxIdleTime,
		EnableWAL:        cfg.Database.EnableWAL,
		EnableForeignKey: cfg.Database.EnableForeignKey,
		BusyTimeout:      cfg.Database.BusyTimeout,
	}

	db, repo, err := database.InitDatabase(dbConfig)
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}
	defer db.Close()

	fmt.Printf("数据库初始化成功\n")

	// 初始化测试数据
	if err := database.InitTestServers(repo); err != nil {
		log.Printf("警告: 测试数据初始化失败: %v", err)
	} else {
		fmt.Printf("测试数据初始化成功\n")
	}

	// 创建系统监控器
	systemMonitor := monitor.NewSystemMonitor(5 * time.Second)

	// 创建WebSocket Hub
	hub := websocket.NewHub(systemMonitor)

	// 启动Hub
	go hub.Run()

	// 设置HTTP路由
	http.HandleFunc("/ws", func(w http.ResponseWriter, r *http.Request) {
		hub.ServeWS(w, r)
	})

	// 提供测试页面
	http.HandleFunc("/", serveTestPage)

	// 提供静态文件服务
	http.HandleFunc("/test.html", serveTestPage)

	// 设置信号处理
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:    *host + ":" + *port,
		Handler: nil,
	}

	// 启动HTTP服务器
	go func() {
		fmt.Printf("WebSocket服务器启动在 %s:%s\n", *host, *port)
		fmt.Printf("测试页面: http://%s:%s/\n", *host, *port)
		fmt.Printf("WebSocket端点: ws://%s:%s/ws\n", *host, *port)
		
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("HTTP服务器启动失败: %v", err)
		}
	}()

	// 定期广播测试消息
	go func() {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				hub.BroadcastMessage("test_message", map[string]interface{}{
					"message": "这是一条测试消息",
					"time":    time.Now().Format("2006-01-02 15:04:05"),
					"clients": hub.GetClientCount(),
				})
			case <-sigCh:
				return
			}
		}
	}()

	fmt.Println("WebSocket服务器运行中，按 Ctrl+C 停止...")

	// 等待信号
	<-sigCh
	fmt.Println("\n收到停止信号，正在停止服务器...")

	// 停止Hub
	hub.Stop()

	// 停止HTTP服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Printf("服务器停止错误: %v", err)
	}

	fmt.Println("WebSocket服务器已停止")
}

// serveTestPage 提供测试页面
func serveTestPage(w http.ResponseWriter, r *http.Request) {
	html := `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WebSocket实时通信测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message { padding: 8px; margin: 5px 0; background: #f8f9fa; border-left: 3px solid #007bff; }
        #messages { height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; }
        input[type="text"] { width: 70%; padding: 8px; }
        button { padding: 8px 15px; margin-left: 10px; }
        .stats { background: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 WebSocket实时通信测试</h1>
        
        <div id="status" class="status disconnected">
            状态: 未连接
        </div>
        
        <div class="stats">
            <strong>连接统计:</strong>
            <span id="stats">等待连接...</span>
        </div>
        
        <div>
            <input type="text" id="messageInput" placeholder="输入消息..." />
            <button onclick="sendMessage()">发送</button>
            <button onclick="sendCommand('get_system_info')">获取系统信息</button>
            <button onclick="sendCommand('get_client_count')">获取客户端数量</button>
        </div>
        
        <h3>📨 消息日志:</h3>
        <div id="messages"></div>
        
        <script>
            let ws;
            let messageCount = 0;
            
            function connect() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = protocol + '//' + window.location.host + '/ws';
                
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    updateStatus('已连接', true);
                    addMessage('系统', '✅ WebSocket连接成功');
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                };
                
                ws.onclose = function(event) {
                    updateStatus('连接关闭', false);
                    addMessage('系统', '❌ WebSocket连接关闭');
                    
                    // 3秒后重连
                    setTimeout(connect, 3000);
                };
                
                ws.onerror = function(error) {
                    updateStatus('连接错误', false);
                    addMessage('系统', '❌ WebSocket连接错误: ' + error);
                };
            }
            
            function updateStatus(text, connected) {
                const statusEl = document.getElementById('status');
                statusEl.textContent = '状态: ' + text;
                statusEl.className = 'status ' + (connected ? 'connected' : 'disconnected');
            }
            
            function handleMessage(data) {
                messageCount++;
                
                switch(data.type) {
                    case 'welcome':
                        addMessage('服务器', '🎉 ' + data.data);
                        break;
                    case 'system_info':
                        addMessage('系统信息', formatSystemInfo(data.data));
                        break;
                    case 'test_message':
                        addMessage('测试', JSON.stringify(data.data, null, 2));
                        break;
                    case 'client_count':
                        updateStats('客户端数量: ' + data.data);
                        break;
                    default:
                        addMessage(data.type, JSON.stringify(data.data, null, 2));
                }
                
                updateStats('已接收消息: ' + messageCount);
            }
            
            function formatSystemInfo(info) {
                return 'CPU: ' + info.cpu_usage.toFixed(1) + '%, ' +
                       '内存: ' + info.memory_usage.toFixed(1) + '%, ' +
                       '磁盘: ' + info.disk_usage.toFixed(1) + '%';
            }
            
            function addMessage(type, content) {
                const messagesEl = document.getElementById('messages');
                const messageEl = document.createElement('div');
                messageEl.className = 'message';
                messageEl.innerHTML = '<strong>' + type + ':</strong> ' + content + 
                                    ' <small>(' + new Date().toLocaleTimeString() + ')</small>';
                messagesEl.appendChild(messageEl);
                messagesEl.scrollTop = messagesEl.scrollHeight;
            }
            
            function updateStats(text) {
                document.getElementById('stats').textContent = text;
            }
            
            function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (message && ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(message);
                    addMessage('我', message);
                    input.value = '';
                }
            }
            
            function sendCommand(command) {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(command);
                    addMessage('命令', command);
                }
            }
            
            // 回车发送消息
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // 页面加载时连接
            connect();
        </script>
    </div>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}
